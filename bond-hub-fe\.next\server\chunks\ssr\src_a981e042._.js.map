{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/contact/ContactSidebar.tsx"], "sourcesContent": ["\"use client\";\r\nimport { memo, useMemo, useEffect } from \"react\";\r\nimport { Users, UserPlus, UsersRound } from \"lucide-react\";\r\nimport { useFriendStore } from \"@/stores/friendStore\";\r\n\r\ntype ContactSidebarProps = {\r\n  activeTab: string;\r\n  setActiveTab: (tab: string) => void;\r\n};\r\n\r\n// Define tabs outside component to prevent recreation on each render\r\nconst tabs = [\r\n  { id: \"friends\", label: \"Danh sách bạn bè\", icon: Users },\r\n  { id: \"groups\", label: \"Danh sách nhóm\", icon: UsersRound },\r\n  { id: \"requests\", label: \"Lời mời kết bạn\", icon: UserPlus },\r\n];\r\n\r\n// Use memo to prevent unnecessary re-renders\r\nfunction ContactSidebar({ activeTab, setActiveTab }: ContactSidebarProps) {\r\n  // Get unread friend requests count from store\r\n  const { unreadReceivedRequests, markFriendRequestsAsRead } = useFriendStore();\r\n\r\n  // Memoize the tab click handler for each tab\r\n  const handleTabClick = useMemo(() => {\r\n    const handlers: Record<string, () => void> = {};\r\n\r\n    tabs.forEach((tab) => {\r\n      handlers[tab.id] = () => {\r\n        // If clicking on requests tab, mark requests as read\r\n        if (tab.id === \"requests\") {\r\n          markFriendRequestsAsRead();\r\n        }\r\n        setActiveTab(tab.id);\r\n      };\r\n    });\r\n\r\n    return handlers;\r\n  }, [setActiveTab, markFriendRequestsAsRead]);\r\n\r\n  // Mark requests as read when the component mounts with requests tab active\r\n  useEffect(() => {\r\n    if (activeTab === \"requests\") {\r\n      markFriendRequestsAsRead();\r\n    }\r\n  }, [activeTab, markFriendRequestsAsRead]);\r\n\r\n  return (\r\n    <div className=\"w-[300px] bg-white border-r flex flex-col h-full overflow-hidden\">\r\n      <div className=\"flex-1 overflow-y-auto no-scrollbar\">\r\n        {tabs.map((tab) => (\r\n          <div\r\n            key={tab.id}\r\n            className={`p-4 cursor-pointer hover:bg-[#F1F2F3] mb-1 ${\r\n              activeTab === tab.id ? \"bg-[#dbebff]\" : \"\"\r\n            }`}\r\n            onClick={handleTabClick[tab.id]}\r\n          >\r\n            <div className=\"flex items-center gap-2 justify-between w-full\">\r\n              <div className=\"flex items-center gap-2\">\r\n                <tab.icon className=\"h-5 w-5\" />\r\n                <span className=\"font-semibold text-sm\">{tab.label}</span>\r\n              </div>\r\n              {tab.id === \"requests\" && unreadReceivedRequests > 0 && (\r\n                <div className=\"bg-red-500 text-white text-xs font-bold rounded-full w-5 h-5 flex items-center justify-center\">\r\n                  {unreadReceivedRequests > 9 ? \"9+\" : unreadReceivedRequests}\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\n// Export memoized component\r\nexport default memo(ContactSidebar);\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AACA;AAHA;;;;;AAUA,qEAAqE;AACrE,MAAM,OAAO;IACX;QAAE,IAAI;QAAW,OAAO;QAAoB,MAAM,oMAAA,CAAA,QAAK;IAAC;IACxD;QAAE,IAAI;QAAU,OAAO;QAAkB,MAAM,kNAAA,CAAA,aAAU;IAAC;IAC1D;QAAE,IAAI;QAAY,OAAO;QAAmB,MAAM,8MAAA,CAAA,WAAQ;IAAC;CAC5D;AAED,6CAA6C;AAC7C,SAAS,eAAe,EAAE,SAAS,EAAE,YAAY,EAAuB;IACtE,8CAA8C;IAC9C,MAAM,EAAE,sBAAsB,EAAE,wBAAwB,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD;IAE1E,6CAA6C;IAC7C,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC7B,MAAM,WAAuC,CAAC;QAE9C,KAAK,OAAO,CAAC,CAAC;YACZ,QAAQ,CAAC,IAAI,EAAE,CAAC,GAAG;gBACjB,qDAAqD;gBACrD,IAAI,IAAI,EAAE,KAAK,YAAY;oBACzB;gBACF;gBACA,aAAa,IAAI,EAAE;YACrB;QACF;QAEA,OAAO;IACT,GAAG;QAAC;QAAc;KAAyB;IAE3C,2EAA2E;IAC3E,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,cAAc,YAAY;YAC5B;QACF;IACF,GAAG;QAAC;QAAW;KAAyB;IAExC,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACZ,KAAK,GAAG,CAAC,CAAC,oBACT,8OAAC;oBAEC,WAAW,CAAC,2CAA2C,EACrD,cAAc,IAAI,EAAE,GAAG,iBAAiB,IACxC;oBACF,SAAS,cAAc,CAAC,IAAI,EAAE,CAAC;8BAE/B,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,IAAI,IAAI;wCAAC,WAAU;;;;;;kDACpB,8OAAC;wCAAK,WAAU;kDAAyB,IAAI,KAAK;;;;;;;;;;;;4BAEnD,IAAI,EAAE,KAAK,cAAc,yBAAyB,mBACjD,8OAAC;gCAAI,WAAU;0CACZ,yBAAyB,IAAI,OAAO;;;;;;;;;;;;mBAbtC,IAAI,EAAE;;;;;;;;;;;;;;;AAsBvB;qDAGe,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 140, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/contact/ContactItem.tsx"], "sourcesContent": ["\"use client\";\r\nimport { memo, useState, useEffect } from \"react\";\r\nimport { removeFriend, blockUser } from \"@/actions/friend.action\";\r\nimport { getUserDataById } from \"@/actions/user.action\";\r\nimport { useAuthStore } from \"@/stores/authStore\";\r\nimport { toast } from \"sonner\";\r\nimport { Avatar, AvatarImage, AvatarFallback } from \"@/components/ui/avatar\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport { User } from \"@/types/base\";\r\nimport ProfileDialog from \"@/components/profile/ProfileDialog\";\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from \"@/components/ui/alert-dialog\";\r\n\r\ntype ContactItemProps = {\r\n  user: User; // <PERSON>ử dụng đối tượng User đầy đủ\r\n  onRemove?: (id: string) => void; // Callback when friend is removed\r\n};\r\n\r\nfunction ContactItem({ user, onRemove }: ContactItemProps) {\r\n  // Lấy các thông tin cần thiết từ đối tượng user\r\n  const id = user.id;\r\n  const fullName = user.userInfo?.fullName || \"\";\r\n  const profilePictureUrl = user.userInfo?.profilePictureUrl || \"\";\r\n\r\n  // State để lưu trữ thông tin đầy đủ của user\r\n  const [fullUserData, setFullUserData] = useState<User | null>(null);\r\n\r\n  // Lấy thông tin đầy đủ của user khi component mount hoặc khi user thay đổi\r\n  useEffect(() => {\r\n    const fetchFullUserData = async () => {\r\n      try {\r\n        const result = await getUserDataById(id);\r\n        if (result.success && result.user) {\r\n          setFullUserData(result.user);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error fetching full user data:\", error);\r\n      }\r\n    };\r\n\r\n    fetchFullUserData();\r\n  }, [id]);\r\n  // State for dialogs\r\n  const [showProfileDialog, setShowProfileDialog] = useState(false);\r\n  const [showBlockDialog, setShowBlockDialog] = useState(false);\r\n  const [isBlocking, setIsBlocking] = useState(false);\r\n  const [showRemoveFriendDialog, setShowRemoveFriendDialog] = useState(false);\r\n  const [isRemovingFriend, setIsRemovingFriend] = useState(false);\r\n\r\n  // Effect to ensure cleanup when component unmounts\r\n  useEffect(() => {\r\n    return () => {\r\n      // Force cleanup of any potential overlay issues when component unmounts\r\n      document.body.style.pointerEvents = \"auto\";\r\n    };\r\n  }, []);\r\n\r\n  // Sử dụng trực tiếp đối tượng user được truyền vào\r\n\r\n  // Add a global click handler to ensure dialogs can be closed\r\n  useEffect(() => {\r\n    const handleGlobalClick = () => {\r\n      // Check if any dialogs are open\r\n      if (showProfileDialog || showBlockDialog) {\r\n        console.log(\"Global click handler detected\");\r\n      }\r\n    };\r\n\r\n    // Add the event listener\r\n    document.addEventListener(\"click\", handleGlobalClick);\r\n\r\n    // Clean up\r\n    return () => {\r\n      document.removeEventListener(\"click\", handleGlobalClick);\r\n    };\r\n  }, [showProfileDialog, showBlockDialog]);\r\n\r\n  // Handle remove friend\r\n  const handleRemoveFriend = async () => {\r\n    if (!id) return;\r\n\r\n    setIsRemovingFriend(true);\r\n    try {\r\n      const accessToken = useAuthStore.getState().accessToken || undefined;\r\n      const result = await removeFriend(id, accessToken);\r\n      if (result.success) {\r\n        toast.success(`Đã xóa kết bạn với ${fullName}`);\r\n\r\n        // Force cleanup of any potential overlay issues\r\n        document.body.style.pointerEvents = \"auto\";\r\n\r\n        // Close dialog with a slight delay\r\n        setTimeout(() => {\r\n          setShowRemoveFriendDialog(false);\r\n\r\n          // Call the callback if provided\r\n          if (onRemove) {\r\n            onRemove(id);\r\n          }\r\n        }, 50);\r\n      } else {\r\n        toast.error(`Không thể xóa kết bạn: ${result.error}`);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error removing friend:\", error);\r\n      toast.error(\"Đã xảy ra lỗi khi xóa kết bạn\");\r\n    } finally {\r\n      setIsRemovingFriend(false);\r\n    }\r\n  };\r\n\r\n  // Handle block user\r\n  const handleBlockUser = async () => {\r\n    if (!id) return;\r\n\r\n    setIsBlocking(true);\r\n    try {\r\n      const accessToken = useAuthStore.getState().accessToken || undefined;\r\n      const result = await blockUser(id, accessToken);\r\n      if (result.success) {\r\n        toast.success(`Đã chặn ${fullName}`);\r\n\r\n        // Force cleanup of any potential overlay issues\r\n        document.body.style.pointerEvents = \"auto\";\r\n\r\n        // Close the dialog with a slight delay\r\n        setTimeout(() => {\r\n          setShowBlockDialog(false);\r\n\r\n          // Call the callback if provided to update UI\r\n          if (onRemove) {\r\n            onRemove(id);\r\n          }\r\n        }, 50);\r\n      } else {\r\n        toast.error(`Không thể chặn người dùng: ${result.error}`);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error blocking user:\", error);\r\n      toast.error(\"Đã xảy ra lỗi khi chặn người dùng\");\r\n    } finally {\r\n      setIsBlocking(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <div className=\"group flex items-center justify-between py-3 px-1 hover:bg-[#f0f2f5] relative last:after:hidden after:content-[''] after:absolute after:left-[56px] after:right-0 after:bottom-0 after:h-[0.25px] after:bg-black/20\">\r\n        <div\r\n          className=\"flex items-center cursor-pointer\"\r\n          onClick={() => setShowProfileDialog(true)}\r\n        >\r\n          <Avatar className=\"h-11 w-11 mr-3\">\r\n            {profilePictureUrl && profilePictureUrl !== \"\" && (\r\n              <AvatarImage\r\n                src={profilePictureUrl}\r\n                alt={fullName}\r\n                className=\"object-cover\"\r\n              />\r\n            )}\r\n            <AvatarFallback className=\"text-base font-medium bg-gray-200 text-gray-700\">\r\n              {fullName.charAt(0).toUpperCase()}\r\n            </AvatarFallback>\r\n          </Avatar>\r\n          <div className=\"font-medium text-sm\">{fullName}</div>\r\n        </div>\r\n        <div className=\"flex items-center\">\r\n          <DropdownMenu>\r\n            <DropdownMenuTrigger asChild>\r\n              <button className=\"h-8 w-8 rounded-full flex items-center justify-center hover:bg-gray-200 outline-none focus:outline-none focus:ring-0\">\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  width=\"16\"\r\n                  height=\"16\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  fill=\"none\"\r\n                  stroke=\"currentColor\"\r\n                  strokeWidth=\"2\"\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                >\r\n                  <circle cx=\"12\" cy=\"12\" r=\"1\"></circle>\r\n                  <circle cx=\"19\" cy=\"12\" r=\"1\"></circle>\r\n                  <circle cx=\"5\" cy=\"12\" r=\"1\"></circle>\r\n                </svg>\r\n              </button>\r\n            </DropdownMenuTrigger>\r\n            <DropdownMenuContent align=\"end\" className=\"w-56\">\r\n              <DropdownMenuItem\r\n                className=\"cursor-pointer\"\r\n                onClick={() => setShowProfileDialog(true)}\r\n              >\r\n                Xem thông tin\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem className=\"cursor-pointer\">\r\n                Phân loại\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem className=\"cursor-pointer\">\r\n                Đặt tên gọi nhớ\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem\r\n                className=\"cursor-pointer\"\r\n                onClick={() => setShowBlockDialog(true)}\r\n              >\r\n                Chặn người này\r\n              </DropdownMenuItem>\r\n              <DropdownMenuSeparator />\r\n              <DropdownMenuItem\r\n                className=\"cursor-pointer text-red-500\"\r\n                onClick={() => setShowRemoveFriendDialog(true)}\r\n              >\r\n                Xóa bạn\r\n              </DropdownMenuItem>\r\n            </DropdownMenuContent>\r\n          </DropdownMenu>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Profile Dialog - Using controlled component pattern */}\r\n      <ProfileDialog\r\n        user={fullUserData || user}\r\n        isOpen={showProfileDialog}\r\n        onOpenChange={(open) => {\r\n          console.log(\"Profile dialog onOpenChange:\", open);\r\n          // Always set the state immediately\r\n          setShowProfileDialog(open);\r\n\r\n          // If dialog is closing, ensure cleanup\r\n          if (!open) {\r\n            // Force cleanup of any potential overlay issues\r\n            document.body.style.pointerEvents = \"auto\";\r\n          }\r\n        }}\r\n        isOwnProfile={false}\r\n        onChat={() => {\r\n          // Xử lý khi nhấn nút nhắn tin\r\n          console.log(\"Bắt đầu trò chuyện với:\", fullName);\r\n          toast.success(`Đang mở cuộc trò chuyện với ${fullName}`);\r\n          // Đóng dialog\r\n          setShowProfileDialog(false);\r\n          // TODO: Chuyển hướng đến trang chat hoặc mở chat dialog\r\n        }}\r\n        onCall={() => {\r\n          // Xử lý khi nhấn nút gọi điện\r\n          console.log(\"Bắt đầu cuộc gọi với:\", fullName);\r\n          // Đóng dialog\r\n          setShowProfileDialog(false);\r\n        }}\r\n      />\r\n\r\n      {/* Block User Confirmation Dialog - Using controlled component pattern */}\r\n      <AlertDialog\r\n        open={showBlockDialog}\r\n        onOpenChange={(open) => {\r\n          console.log(\"Block dialog onOpenChange:\", open);\r\n          // Always set the state immediately\r\n          setShowBlockDialog(open);\r\n\r\n          // If dialog is closing, ensure cleanup\r\n          if (!open) {\r\n            // Force cleanup of any potential overlay issues\r\n            document.body.style.pointerEvents = \"auto\";\r\n          }\r\n        }}\r\n      >\r\n        <AlertDialogContent>\r\n          <AlertDialogHeader>\r\n            <AlertDialogTitle>Chặn người dùng</AlertDialogTitle>\r\n            <AlertDialogDescription>\r\n              Bạn có chắc chắn muốn chặn {fullName}? Người này sẽ không thể gửi\r\n              tin nhắn hoặc gọi điện cho bạn nữa.\r\n            </AlertDialogDescription>\r\n          </AlertDialogHeader>\r\n          <AlertDialogFooter>\r\n            <AlertDialogCancel disabled={isBlocking}>Hủy</AlertDialogCancel>\r\n            <AlertDialogAction\r\n              onClick={handleBlockUser}\r\n              disabled={isBlocking}\r\n              className=\"bg-red-500 hover:bg-red-600\"\r\n            >\r\n              {isBlocking ? (\r\n                <>\r\n                  <div className=\"animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent mr-2\"></div>\r\n                  Đang chặn...\r\n                </>\r\n              ) : (\r\n                \"Chặn\"\r\n              )}\r\n            </AlertDialogAction>\r\n          </AlertDialogFooter>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n\r\n      {/* Remove Friend Confirmation Dialog */}\r\n      <AlertDialog\r\n        open={showRemoveFriendDialog}\r\n        onOpenChange={(open) => {\r\n          console.log(\"Remove friend dialog onOpenChange:\", open);\r\n          // Always set the state immediately\r\n          setShowRemoveFriendDialog(open);\r\n\r\n          // If dialog is closing, ensure cleanup\r\n          if (!open) {\r\n            // Force cleanup of any potential overlay issues\r\n            document.body.style.pointerEvents = \"auto\";\r\n          }\r\n        }}\r\n      >\r\n        <AlertDialogContent>\r\n          <AlertDialogHeader>\r\n            <AlertDialogTitle>Xóa bạn bè</AlertDialogTitle>\r\n            <AlertDialogDescription>\r\n              Bạn có chắc chắn muốn xóa kết bạn với {fullName}? Hành động này sẽ\r\n              xóa tất cả các cuộc trò chuyện chung và không thể hoàn tác.\r\n            </AlertDialogDescription>\r\n          </AlertDialogHeader>\r\n          <AlertDialogFooter>\r\n            <AlertDialogCancel disabled={isRemovingFriend}>\r\n              Hủy\r\n            </AlertDialogCancel>\r\n            <AlertDialogAction\r\n              onClick={handleRemoveFriend}\r\n              disabled={isRemovingFriend}\r\n              className=\"bg-red-500 hover:bg-red-600\"\r\n            >\r\n              {isRemovingFriend ? (\r\n                <>\r\n                  <div className=\"animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent mr-2\"></div>\r\n                  Đang xóa...\r\n                </>\r\n              ) : (\r\n                \"Xóa bạn bè\"\r\n              )}\r\n            </AlertDialogAction>\r\n          </AlertDialogFooter>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n    </>\r\n  );\r\n}\r\n\r\n// Export memoized component to prevent unnecessary re-renders\r\nexport default memo(ContactItem);\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAQA;AACA;AAhBA;;;;;;;;;;;AAgCA,SAAS,YAAY,EAAE,IAAI,EAAE,QAAQ,EAAoB;IACvD,gDAAgD;IAChD,MAAM,KAAK,KAAK,EAAE;IAClB,MAAM,WAAW,KAAK,QAAQ,EAAE,YAAY;IAC5C,MAAM,oBAAoB,KAAK,QAAQ,EAAE,qBAAqB;IAE9D,6CAA6C;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAE9D,2EAA2E;IAC3E,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,oBAAoB;YACxB,IAAI;gBACF,MAAM,SAAS,MAAM,CAAA,GAAA,gIAAA,CAAA,kBAAe,AAAD,EAAE;gBACrC,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;oBACjC,gBAAgB,OAAO,IAAI;gBAC7B;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,kCAAkC;YAClD;QACF;QAEA;IACF,GAAG;QAAC;KAAG;IACP,oBAAoB;IACpB,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,mDAAmD;IACnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL,wEAAwE;YACxE,SAAS,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG;QACtC;IACF,GAAG,EAAE;IAEL,mDAAmD;IAEnD,6DAA6D;IAC7D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,oBAAoB;YACxB,gCAAgC;YAChC,IAAI,qBAAqB,iBAAiB;gBACxC,QAAQ,GAAG,CAAC;YACd;QACF;QAEA,yBAAyB;QACzB,SAAS,gBAAgB,CAAC,SAAS;QAEnC,WAAW;QACX,OAAO;YACL,SAAS,mBAAmB,CAAC,SAAS;QACxC;IACF,GAAG;QAAC;QAAmB;KAAgB;IAEvC,uBAAuB;IACvB,MAAM,qBAAqB;QACzB,IAAI,CAAC,IAAI;QAET,oBAAoB;QACpB,IAAI;YACF,MAAM,cAAc,0HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,WAAW,IAAI;YAC3D,MAAM,SAAS,MAAM,CAAA,GAAA,sJAAA,CAAA,eAAY,AAAD,EAAE,IAAI;YACtC,IAAI,OAAO,OAAO,EAAE;gBAClB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,mBAAmB,EAAE,UAAU;gBAE9C,gDAAgD;gBAChD,SAAS,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG;gBAEpC,mCAAmC;gBACnC,WAAW;oBACT,0BAA0B;oBAE1B,gCAAgC;oBAChC,IAAI,UAAU;wBACZ,SAAS;oBACX;gBACF,GAAG;YACL,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,uBAAuB,EAAE,OAAO,KAAK,EAAE;YACtD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,oBAAoB;QACtB;IACF;IAEA,oBAAoB;IACpB,MAAM,kBAAkB;QACtB,IAAI,CAAC,IAAI;QAET,cAAc;QACd,IAAI;YACF,MAAM,cAAc,0HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,WAAW,IAAI;YAC3D,MAAM,SAAS,MAAM,CAAA,GAAA,sJAAA,CAAA,YAAS,AAAD,EAAE,IAAI;YACnC,IAAI,OAAO,OAAO,EAAE;gBAClB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,UAAU;gBAEnC,gDAAgD;gBAChD,SAAS,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG;gBAEpC,uCAAuC;gBACvC,WAAW;oBACT,mBAAmB;oBAEnB,6CAA6C;oBAC7C,IAAI,UAAU;wBACZ,SAAS;oBACX;gBACF,GAAG;YACL,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,2BAA2B,EAAE,OAAO,KAAK,EAAE;YAC1D;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,cAAc;QAChB;IACF;IAEA,qBACE;;0BACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,WAAU;wBACV,SAAS,IAAM,qBAAqB;;0CAEpC,8OAAC,kIAAA,CAAA,SAAM;gCAAC,WAAU;;oCACf,qBAAqB,sBAAsB,oBAC1C,8OAAC,kIAAA,CAAA,cAAW;wCACV,KAAK;wCACL,KAAK;wCACL,WAAU;;;;;;kDAGd,8OAAC,kIAAA,CAAA,iBAAc;wCAAC,WAAU;kDACvB,SAAS,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;;0CAGnC,8OAAC;gCAAI,WAAU;0CAAuB;;;;;;;;;;;;kCAExC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4IAAA,CAAA,eAAY;;8CACX,8OAAC,4IAAA,CAAA,sBAAmB;oCAAC,OAAO;8CAC1B,cAAA,8OAAC;wCAAO,WAAU;kDAChB,cAAA,8OAAC;4CACC,OAAM;4CACN,OAAM;4CACN,QAAO;4CACP,SAAQ;4CACR,MAAK;4CACL,QAAO;4CACP,aAAY;4CACZ,eAAc;4CACd,gBAAe;;8DAEf,8OAAC;oDAAO,IAAG;oDAAK,IAAG;oDAAK,GAAE;;;;;;8DAC1B,8OAAC;oDAAO,IAAG;oDAAK,IAAG;oDAAK,GAAE;;;;;;8DAC1B,8OAAC;oDAAO,IAAG;oDAAI,IAAG;oDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;8CAI/B,8OAAC,4IAAA,CAAA,sBAAmB;oCAAC,OAAM;oCAAM,WAAU;;sDACzC,8OAAC,4IAAA,CAAA,mBAAgB;4CACf,WAAU;4CACV,SAAS,IAAM,qBAAqB;sDACrC;;;;;;sDAGD,8OAAC,4IAAA,CAAA,mBAAgB;4CAAC,WAAU;sDAAiB;;;;;;sDAG7C,8OAAC,4IAAA,CAAA,mBAAgB;4CAAC,WAAU;sDAAiB;;;;;;sDAG7C,8OAAC,4IAAA,CAAA,mBAAgB;4CACf,WAAU;4CACV,SAAS,IAAM,mBAAmB;sDACnC;;;;;;sDAGD,8OAAC,4IAAA,CAAA,wBAAqB;;;;;sDACtB,8OAAC,4IAAA,CAAA,mBAAgB;4CACf,WAAU;4CACV,SAAS,IAAM,0BAA0B;sDAC1C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC,8IAAA,CAAA,UAAa;gBACZ,MAAM,gBAAgB;gBACtB,QAAQ;gBACR,cAAc,CAAC;oBACb,QAAQ,GAAG,CAAC,gCAAgC;oBAC5C,mCAAmC;oBACnC,qBAAqB;oBAErB,uCAAuC;oBACvC,IAAI,CAAC,MAAM;wBACT,gDAAgD;wBAChD,SAAS,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG;oBACtC;gBACF;gBACA,cAAc;gBACd,QAAQ;oBACN,8BAA8B;oBAC9B,QAAQ,GAAG,CAAC,2BAA2B;oBACvC,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,4BAA4B,EAAE,UAAU;oBACvD,cAAc;oBACd,qBAAqB;gBACrB,wDAAwD;gBAC1D;gBACA,QAAQ;oBACN,8BAA8B;oBAC9B,QAAQ,GAAG,CAAC,yBAAyB;oBACrC,cAAc;oBACd,qBAAqB;gBACvB;;;;;;0BAIF,8OAAC,2IAAA,CAAA,cAAW;gBACV,MAAM;gBACN,cAAc,CAAC;oBACb,QAAQ,GAAG,CAAC,8BAA8B;oBAC1C,mCAAmC;oBACnC,mBAAmB;oBAEnB,uCAAuC;oBACvC,IAAI,CAAC,MAAM;wBACT,gDAAgD;wBAChD,SAAS,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG;oBACtC;gBACF;0BAEA,cAAA,8OAAC,2IAAA,CAAA,qBAAkB;;sCACjB,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,8OAAC,2IAAA,CAAA,yBAAsB;;wCAAC;wCACM;wCAAS;;;;;;;;;;;;;sCAIzC,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,oBAAiB;oCAAC,UAAU;8CAAY;;;;;;8CACzC,8OAAC,2IAAA,CAAA,oBAAiB;oCAChB,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,2BACC;;0DACE,8OAAC;gDAAI,WAAU;;;;;;4CAA0F;;uDAI3G;;;;;;;;;;;;;;;;;;;;;;;0BAQV,8OAAC,2IAAA,CAAA,cAAW;gBACV,MAAM;gBACN,cAAc,CAAC;oBACb,QAAQ,GAAG,CAAC,sCAAsC;oBAClD,mCAAmC;oBACnC,0BAA0B;oBAE1B,uCAAuC;oBACvC,IAAI,CAAC,MAAM;wBACT,gDAAgD;wBAChD,SAAS,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG;oBACtC;gBACF;0BAEA,cAAA,8OAAC,2IAAA,CAAA,qBAAkB;;sCACjB,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,8OAAC,2IAAA,CAAA,yBAAsB;;wCAAC;wCACiB;wCAAS;;;;;;;;;;;;;sCAIpD,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,oBAAiB;oCAAC,UAAU;8CAAkB;;;;;;8CAG/C,8OAAC,2IAAA,CAAA,oBAAiB;oCAChB,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,iCACC;;0DACE,8OAAC;gDAAI,WAAU;;;;;;4CAA0F;;uDAI3G;;;;;;;;;;;;;;;;;;;;;;;;;AAQhB;qDAGe,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 683, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/contact/ContactList.tsx"], "sourcesContent": ["\"use client\";\r\nimport { useMemo, useState, memo, useCallback, useEffect } from \"react\";\r\nimport ContactItem from \"./ContactItem\";\r\nimport { toast } from \"sonner\";\r\nimport { User } from \"@/types/base\";\r\n\r\ntype Friend = {\r\n  id: string;\r\n  fullName: string;\r\n  profilePictureUrl: string;\r\n  email?: string;\r\n  phoneNumber?: string;\r\n};\r\n\r\ntype FriendsByLetter = {\r\n  [key: string]: Friend[];\r\n};\r\n\r\ntype ContactListProps = {\r\n  friends: Friend[];\r\n};\r\n\r\nfunction ContactList({ friends: initialFriends }: ContactListProps) {\r\n  const [friends, setFriends] = useState<Friend[]>(initialFriends);\r\n  const [searchQuery, setSearchQuery] = useState<string>(\"\");\r\n  const [sortOption, setSortOption] = useState<string>(\"name\");\r\n\r\n  // Update friends when initialFriends changes\r\n  useEffect(() => {\r\n    setFriends(initialFriends);\r\n  }, [initialFriends]);\r\n\r\n  // Handle friend removal\r\n  const handleRemoveFriend = useCallback((id: string) => {\r\n    // Update local state to remove the friend\r\n    setFriends((prevFriends) =>\r\n      prevFriends.filter((friend) => friend.id !== id),\r\n    );\r\n    toast.success(\"Xóa kết bạn thành công\");\r\n  }, []);\r\n\r\n  // Memoize the search input handler\r\n  const handleSearchChange = useCallback(\r\n    (e: React.ChangeEvent<HTMLInputElement>) => {\r\n      setSearchQuery(e.target.value);\r\n    },\r\n    [],\r\n  );\r\n\r\n  // Memoize the sort option handler\r\n  const handleSortChange = useCallback(\r\n    (e: React.ChangeEvent<HTMLSelectElement>) => {\r\n      setSortOption(e.target.value);\r\n    },\r\n    [],\r\n  );\r\n\r\n  // Filter and sort friends\r\n  const filteredAndSortedFriends = useMemo(() => {\r\n    // Filter by search query\r\n    const filtered = searchQuery\r\n      ? friends.filter((friend) =>\r\n          friend.fullName.toLowerCase().includes(searchQuery.toLowerCase()),\r\n        )\r\n      : friends;\r\n\r\n    // Sort by selected option\r\n    return [...filtered].sort((a, b) => {\r\n      // Always sort by name since we removed status\r\n      return a.fullName.localeCompare(b.fullName);\r\n    });\r\n  }, [friends, searchQuery]);\r\n\r\n  // Group friends by first letter\r\n  const friendsByLetter = useMemo(() => {\r\n    const result: FriendsByLetter = {};\r\n    filteredAndSortedFriends.forEach((friend) => {\r\n      const firstLetter = friend.fullName.charAt(0).toUpperCase();\r\n      if (!result[firstLetter]) {\r\n        result[firstLetter] = [];\r\n      }\r\n      result[firstLetter].push(friend);\r\n    });\r\n    return result;\r\n  }, [filteredAndSortedFriends]);\r\n\r\n  return (\r\n    <div className=\"h-full w-full bg-white rounded-md shadow-sm overflow-hidden flex flex-col no-scrollbar\">\r\n      <div className=\"p-4 flex items-center justify-between\">\r\n        <div className=\"relative w-full\">\r\n          <svg\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            width=\"16\"\r\n            height=\"16\"\r\n            viewBox=\"0 0 24 24\"\r\n            fill=\"none\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth=\"2\"\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n            className=\"absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500\"\r\n          >\r\n            <circle cx=\"11\" cy=\"11\" r=\"8\"></circle>\r\n            <path d=\"m21 21-4.3-4.3\"></path>\r\n          </svg>\r\n          <input\r\n            type=\"text\"\r\n            placeholder=\"Tìm bạn\"\r\n            className=\"w-full h-8 bg-white border border-gray-200/50 rounded-md pl-8 outline-none text-sm focus:border-blue-300/50 transition-colors\"\r\n            value={searchQuery}\r\n            onChange={handleSearchChange}\r\n          />\r\n        </div>\r\n\r\n        <div className=\"flex items-center ml-4 space-x-2\">\r\n          <div className=\"relative\">\r\n            <select\r\n              className=\"appearance-none bg-white border border-gray-200 rounded-md px-3 py-1 pr-8 text-sm cursor-pointer outline-none\"\r\n              value={sortOption}\r\n              onChange={handleSortChange}\r\n            >\r\n              <option value=\"name\">Tên (A-Z)</option>\r\n              <option value=\"status\">Trạng thái</option>\r\n            </select>\r\n            <div className=\"pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700\">\r\n              <svg\r\n                className=\"fill-current h-4 w-4\"\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                viewBox=\"0 0 20 20\"\r\n              >\r\n                <path d=\"M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z\" />\r\n              </svg>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"relative\">\r\n            <select className=\"appearance-none bg-white border border-gray-200 rounded-md px-3 py-1 pr-8 text-sm cursor-pointer outline-none\">\r\n              <option>Tất cả</option>\r\n            </select>\r\n            <div className=\"pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700\">\r\n              <svg\r\n                className=\"fill-current h-4 w-4\"\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                viewBox=\"0 0 20 20\"\r\n              >\r\n                <path d=\"M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z\" />\r\n              </svg>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"flex-1 overflow-y-auto p-4 no-scrollbar\">\r\n        {Object.keys(friendsByLetter)\r\n          .sort()\r\n          .map((letter) => (\r\n            <div key={letter} className=\"mb-6\">\r\n              <div className=\"text-base font-semibold mb-2 text-gray-700\">\r\n                {letter}\r\n              </div>\r\n              {friendsByLetter[letter].map((friend) => (\r\n                <ContactItem\r\n                  key={friend.id}\r\n                  user={\r\n                    {\r\n                      id: friend.id,\r\n                      userInfo: {\r\n                        fullName: friend.fullName,\r\n                        profilePictureUrl: friend.profilePictureUrl,\r\n                      },\r\n                    } as unknown as User\r\n                  }\r\n                  onRemove={handleRemoveFriend}\r\n                />\r\n              ))}\r\n            </div>\r\n          ))}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\n// Export memoized component to prevent unnecessary re-renders\r\nexport default memo(ContactList);\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAHA;;;;;AAsBA,SAAS,YAAY,EAAE,SAAS,cAAc,EAAoB;IAChE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACvD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAErD,6CAA6C;IAC7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG;QAAC;KAAe;IAEnB,wBAAwB;IACxB,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACtC,0CAA0C;QAC1C,WAAW,CAAC,cACV,YAAY,MAAM,CAAC,CAAC,SAAW,OAAO,EAAE,KAAK;QAE/C,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB,GAAG,EAAE;IAEL,mCAAmC;IACnC,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACnC,CAAC;QACC,eAAe,EAAE,MAAM,CAAC,KAAK;IAC/B,GACA,EAAE;IAGJ,kCAAkC;IAClC,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACjC,CAAC;QACC,cAAc,EAAE,MAAM,CAAC,KAAK;IAC9B,GACA,EAAE;IAGJ,0BAA0B;IAC1B,MAAM,2BAA2B,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACvC,yBAAyB;QACzB,MAAM,WAAW,cACb,QAAQ,MAAM,CAAC,CAAC,SACd,OAAO,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAEhE;QAEJ,0BAA0B;QAC1B,OAAO;eAAI;SAAS,CAAC,IAAI,CAAC,CAAC,GAAG;YAC5B,8CAA8C;YAC9C,OAAO,EAAE,QAAQ,CAAC,aAAa,CAAC,EAAE,QAAQ;QAC5C;IACF,GAAG;QAAC;QAAS;KAAY;IAEzB,gCAAgC;IAChC,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC9B,MAAM,SAA0B,CAAC;QACjC,yBAAyB,OAAO,CAAC,CAAC;YAChC,MAAM,cAAc,OAAO,QAAQ,CAAC,MAAM,CAAC,GAAG,WAAW;YACzD,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;gBACxB,MAAM,CAAC,YAAY,GAAG,EAAE;YAC1B;YACA,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC;QAC3B;QACA,OAAO;IACT,GAAG;QAAC;KAAyB;IAE7B,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,OAAM;gCACN,OAAM;gCACN,QAAO;gCACP,SAAQ;gCACR,MAAK;gCACL,QAAO;gCACP,aAAY;gCACZ,eAAc;gCACd,gBAAe;gCACf,WAAU;;kDAEV,8OAAC;wCAAO,IAAG;wCAAK,IAAG;wCAAK,GAAE;;;;;;kDAC1B,8OAAC;wCAAK,GAAE;;;;;;;;;;;;0CAEV,8OAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,WAAU;gCACV,OAAO;gCACP,UAAU;;;;;;;;;;;;kCAId,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,WAAU;wCACV,OAAO;wCACP,UAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,8OAAC;gDAAO,OAAM;0DAAS;;;;;;;;;;;;kDAEzB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,WAAU;4CACV,OAAM;4CACN,SAAQ;sDAER,cAAA,8OAAC;gDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;0CAKd,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAO,WAAU;kDAChB,cAAA,8OAAC;sDAAO;;;;;;;;;;;kDAEV,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,WAAU;4CACV,OAAM;4CACN,SAAQ;sDAER,cAAA,8OAAC;gDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOlB,8OAAC;gBAAI,WAAU;0BACZ,OAAO,IAAI,CAAC,iBACV,IAAI,GACJ,GAAG,CAAC,CAAC,uBACJ,8OAAC;wBAAiB,WAAU;;0CAC1B,8OAAC;gCAAI,WAAU;0CACZ;;;;;;4BAEF,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,uBAC5B,8OAAC,4IAAA,CAAA,UAAW;oCAEV,MACE;wCACE,IAAI,OAAO,EAAE;wCACb,UAAU;4CACR,UAAU,OAAO,QAAQ;4CACzB,mBAAmB,OAAO,iBAAiB;wCAC7C;oCACF;oCAEF,UAAU;mCAVL,OAAO,EAAE;;;;;;uBANV;;;;;;;;;;;;;;;;AAwBtB;qDAGe,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 980, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/contact/GroupList.tsx"], "sourcesContent": ["\"use client\";\r\nimport { memo, useState, useMemo } from \"react\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { Avatar, AvatarImage, AvatarFallback } from \"@/components/ui/avatar\";\r\nimport { useChatStore } from \"@/stores/chatStore\";\r\n\r\ntype GroupItemProps = {\r\n  id: string;\r\n  name: string;\r\n  memberCount: number;\r\n  imageUrl: string;\r\n  avatarUrl?: string | null;\r\n  onClick?: (id: string) => void;\r\n};\r\n\r\nfunction GroupItem({\r\n  id,\r\n  name,\r\n  memberCount,\r\n  imageUrl,\r\n  avatarUrl,\r\n  onClick,\r\n}: GroupItemProps) {\r\n  const handleClick = () => {\r\n    if (onClick) {\r\n      onClick(id);\r\n    }\r\n  };\r\n  return (\r\n    <div\r\n      className=\"group flex items-center justify-between py-3 px-1 hover:bg-[#f0f2f5] cursor-pointer relative last:after:hidden after:content-[''] after:absolute after:left-[56px] after:right-0 after:bottom-0 after:h-[0.25px] after:bg-black/20\"\r\n      onClick={handleClick}\r\n    >\r\n      <div className=\"flex items-center\">\r\n        <Avatar className=\"h-11 w-11 mr-3\">\r\n          {(avatarUrl || imageUrl) && (\r\n            <AvatarImage\r\n              src={avatarUrl || imageUrl}\r\n              alt={name}\r\n              className=\"object-cover\"\r\n            />\r\n          )}\r\n          <AvatarFallback className=\"text-base font-medium bg-gray-200 text-gray-700\">\r\n            {name.charAt(0).toUpperCase()}\r\n          </AvatarFallback>\r\n        </Avatar>\r\n        <div className=\"flex flex-col\">\r\n          <div className=\"font-medium text-sm\">{name}</div>\r\n          <div className=\"text-xs text-gray-500\">{memberCount} thành viên</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\ntype GroupListProps = {\r\n  groups: GroupItemProps[];\r\n};\r\n\r\nfunction GroupList({ groups }: GroupListProps) {\r\n  const router = useRouter();\r\n  const { openChat } = useChatStore();\r\n  const [searchQuery, setSearchQuery] = useState(\"\");\r\n\r\n  const handleGroupClick = async (groupId: string) => {\r\n    try {\r\n      // Open the chat with the selected group\r\n      const success = await openChat(groupId, \"GROUP\");\r\n\r\n      if (success) {\r\n        // Navigate to the chat page\r\n        router.push(`/dashboard/chat?groupId=${groupId}`);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error opening group chat:\", error);\r\n    }\r\n  };\r\n  // Filter groups based on search query\r\n  const filteredGroups = useMemo(() => {\r\n    if (!searchQuery.trim()) return groups;\r\n\r\n    return groups.filter((group) =>\r\n      group.name.toLowerCase().includes(searchQuery.toLowerCase()),\r\n    );\r\n  }, [groups, searchQuery]);\r\n\r\n  return (\r\n    <div className=\"bg-white rounded-md shadow-sm overflow-hidden no-scrollbar\">\r\n      <div className=\"p-4 bg-white\">\r\n        <div className=\"flex items-center justify-between mb-4\">\r\n          <div className=\"relative w-full\">\r\n            <div className=\"flex items-center border bg-[#ebecf0] rounded-md px-2 h-8 w-full\">\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                width=\"16\"\r\n                height=\"16\"\r\n                viewBox=\"0 0 24 24\"\r\n                fill=\"none\"\r\n                stroke=\"currentColor\"\r\n                strokeWidth=\"2\"\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n                className=\"h-4 w-4 flex-shrink-0 my-auto\"\r\n              >\r\n                <circle cx=\"11\" cy=\"11\" r=\"8\"></circle>\r\n                <path d=\"m21 21-4.3-4.3\"></path>\r\n              </svg>\r\n              <input\r\n                placeholder=\"Tìm kiếm\"\r\n                className=\"border-0 h-8 bg-transparent outline-none w-full placeholder:text-[0.8125rem] ml-2 py-0\"\r\n                value={searchQuery}\r\n                onChange={(e) => setSearchQuery(e.target.value)}\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"space-y-1 overflow-auto no-scrollbar\">\r\n          {filteredGroups.length > 0 ? (\r\n            filteredGroups.map((group) => (\r\n              <GroupItem\r\n                key={group.id}\r\n                id={group.id}\r\n                name={group.name}\r\n                memberCount={group.memberCount}\r\n                imageUrl={group.imageUrl}\r\n                avatarUrl={group.avatarUrl}\r\n                onClick={handleGroupClick}\r\n              />\r\n            ))\r\n          ) : (\r\n            <div className=\"text-center py-4 text-gray-500\">\r\n              Không tìm thấy nhóm nào phù hợp\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default memo(GroupList);\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AAJA;;;;;;AAeA,SAAS,UAAU,EACjB,EAAE,EACF,IAAI,EACJ,WAAW,EACX,QAAQ,EACR,SAAS,EACT,OAAO,EACQ;IACf,MAAM,cAAc;QAClB,IAAI,SAAS;YACX,QAAQ;QACV;IACF;IACA,qBACE,8OAAC;QACC,WAAU;QACV,SAAS;kBAET,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,kIAAA,CAAA,SAAM;oBAAC,WAAU;;wBACf,CAAC,aAAa,QAAQ,mBACrB,8OAAC,kIAAA,CAAA,cAAW;4BACV,KAAK,aAAa;4BAClB,KAAK;4BACL,WAAU;;;;;;sCAGd,8OAAC,kIAAA,CAAA,iBAAc;4BAAC,WAAU;sCACvB,KAAK,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;;8BAG/B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAuB;;;;;;sCACtC,8OAAC;4BAAI,WAAU;;gCAAyB;gCAAY;;;;;;;;;;;;;;;;;;;;;;;;AAK9D;AAMA,SAAS,UAAU,EAAE,MAAM,EAAkB;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD;IAChC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,wCAAwC;YACxC,MAAM,UAAU,MAAM,SAAS,SAAS;YAExC,IAAI,SAAS;gBACX,4BAA4B;gBAC5B,OAAO,IAAI,CAAC,CAAC,wBAAwB,EAAE,SAAS;YAClD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;IACA,sCAAsC;IACtC,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC7B,IAAI,CAAC,YAAY,IAAI,IAAI,OAAO;QAEhC,OAAO,OAAO,MAAM,CAAC,CAAC,QACpB,MAAM,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;IAE7D,GAAG;QAAC;QAAQ;KAAY;IAExB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,OAAM;oCACN,OAAM;oCACN,QAAO;oCACP,SAAQ;oCACR,MAAK;oCACL,QAAO;oCACP,aAAY;oCACZ,eAAc;oCACd,gBAAe;oCACf,WAAU;;sDAEV,8OAAC;4CAAO,IAAG;4CAAK,IAAG;4CAAK,GAAE;;;;;;sDAC1B,8OAAC;4CAAK,GAAE;;;;;;;;;;;;8CAEV,8OAAC;oCACC,aAAY;oCACZ,WAAU;oCACV,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;8BAMtD,8OAAC;oBAAI,WAAU;8BACZ,eAAe,MAAM,GAAG,IACvB,eAAe,GAAG,CAAC,CAAC,sBAClB,8OAAC;4BAEC,IAAI,MAAM,EAAE;4BACZ,MAAM,MAAM,IAAI;4BAChB,aAAa,MAAM,WAAW;4BAC9B,UAAU,MAAM,QAAQ;4BACxB,WAAW,MAAM,SAAS;4BAC1B,SAAS;2BANJ,MAAM,EAAE;;;;kDAUjB,8OAAC;wBAAI,WAAU;kCAAiC;;;;;;;;;;;;;;;;;;;;;;AAQ5D;qDAEe,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 1213, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/contact/FriendRequests.tsx"], "sourcesContent": ["\"use client\";\r\nimport { memo, useState } from \"react\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { useFriendStore } from \"@/stores/friendStore\";\r\nimport { toast } from \"sonner\";\r\nimport { Avatar, AvatarImage, AvatarFallback } from \"@/components/ui/avatar\";\r\nimport Image from \"next/image\";\r\nimport ProfileDialog from \"@/components/profile/ProfileDialog\";\r\nimport { User } from \"@/types/base\";\r\n\r\ntype FriendRequestProps = {\r\n  id: string;\r\n  fullName: string;\r\n  profilePictureUrl: string;\r\n  message: string;\r\n  timeAgo: string;\r\n  userData?: User; // Thêm trường userData để lưu thông tin đầy đủ của người dùng\r\n};\r\n\r\ntype SentRequestProps = {\r\n  id: string;\r\n  fullName: string;\r\n  profilePictureUrl: string;\r\n  timeAgo: string;\r\n  userData?: User; // Thêm trường userData để lưu thông tin đầy đủ của người dùng\r\n};\r\n\r\nfunction FriendRequestItem({\r\n  id,\r\n  fullName,\r\n  profilePictureUrl,\r\n  message,\r\n  timeAgo,\r\n  userData,\r\n}: FriendRequestProps) {\r\n  const [isAccepting, setIsAccepting] = useState(false);\r\n  const [isRejecting, setIsRejecting] = useState(false);\r\n  const [showProfileDialog, setShowProfileDialog] = useState(false);\r\n  const { acceptRequest, rejectRequest } = useFriendStore();\r\n\r\n  const handleAccept = async () => {\r\n    setIsAccepting(true);\r\n    try {\r\n      const success = await acceptRequest(id);\r\n      if (success) {\r\n        toast.success(`Đã chấp nhận lời mời kết bạn từ ${fullName}`);\r\n      } else {\r\n        toast.error(\r\n          \"Không thể chấp nhận lời mời kết bạn. Vui lòng thử lại sau.\",\r\n        );\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error accepting friend request:\", error);\r\n      toast.error(\"Đã xảy ra lỗi khi chấp nhận lời mời kết bạn\");\r\n    } finally {\r\n      setIsAccepting(false);\r\n    }\r\n  };\r\n\r\n  const handleReject = async () => {\r\n    setIsRejecting(true);\r\n    try {\r\n      const success = await rejectRequest(id);\r\n      if (success) {\r\n        toast.success(`Đã từ chối lời mời kết bạn từ ${fullName}`);\r\n      } else {\r\n        toast.error(\"Không thể từ chối lời mời kết bạn. Vui lòng thử lại sau.\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error rejecting friend request:\", error);\r\n      toast.error(\"Đã xảy ra lỗi khi từ chối lời mời kết bạn\");\r\n    } finally {\r\n      setIsRejecting(false);\r\n    }\r\n  };\r\n\r\n  // Hàm xử lý khi nhấn vào card để xem profile\r\n  const handleViewProfile = () => {\r\n    // Log để kiểm tra userData\r\n    console.log(\"userData in FriendRequestItem:\", userData);\r\n\r\n    // Nếu có userData (thông tin đầy đủ của người dùng), sử dụng nó\r\n    if (userData) {\r\n      // Hiển thị dialog profile với thông tin đầy đủ\r\n      setShowProfileDialog(true);\r\n    } else {\r\n      // Nếu không có userData, hiển thị thông báo\r\n      console.error(\"userData is undefined, cannot show profile dialog\");\r\n      toast.error(\"Không thể hiển thị thông tin người dùng\");\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <div className=\"bg-white rounded-md p-4 mb-4 w-[302px] shadow-sm\">\r\n        <div\r\n          className=\"flex items-start justify-between mb-2 cursor-pointer\"\r\n          onClick={handleViewProfile}\r\n        >\r\n          <div className=\"flex items-center\">\r\n            <Avatar className=\"h-10 w-10 mr-3 flex-shrink-0\">\r\n              {profilePictureUrl && profilePictureUrl !== \"\" && (\r\n                <AvatarImage\r\n                  src={profilePictureUrl}\r\n                  alt={fullName}\r\n                  className=\"object-cover\"\r\n                />\r\n              )}\r\n              <AvatarFallback className=\"text-sm font-medium bg-gray-200 text-gray-700\">\r\n                {fullName.charAt(0).toUpperCase()}\r\n              </AvatarFallback>\r\n            </Avatar>\r\n            <div>\r\n              <div className=\"font-semibold text-sm\">{fullName}</div>\r\n              <div className=\"text-xs text-gray-500\">{timeAgo}</div>\r\n            </div>\r\n          </div>\r\n          <button\r\n            className=\"text-gray-500 hover:text-gray-700\"\r\n            onClick={(e) => {\r\n              e.stopPropagation(); // Ngăn chặn sự kiện click lan truyền\r\n              handleViewProfile();\r\n            }}\r\n          >\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              width=\"20\"\r\n              height=\"20\"\r\n              viewBox=\"0 0 24 24\"\r\n              fill=\"none\"\r\n              stroke=\"currentColor\"\r\n              strokeWidth=\"2\"\r\n              strokeLinecap=\"round\"\r\n              strokeLinejoin=\"round\"\r\n            >\r\n              <path d=\"M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z\"></path>\r\n              <polyline points=\"22,6 12,13 2,6\"></polyline>\r\n            </svg>\r\n          </button>\r\n        </div>\r\n\r\n        <div\r\n          className=\"text-sm text-gray-700 mb-4 bg-[#ebecf0] p-3 rounded-md cursor-pointer\"\r\n          onClick={handleViewProfile}\r\n        >\r\n          {message.length > 150 ? `${message.substring(0, 150)}...` : message}\r\n        </div>\r\n\r\n        <div className=\"flex gap-2\">\r\n          <Button\r\n            variant=\"outline\"\r\n            className=\"flex-1 bg-[#e5e7eb] hover:bg-gray-300 border-gray-200 text-sm h-10 font-semibold\"\r\n            onClick={(e) => {\r\n              e.stopPropagation();\r\n              handleReject();\r\n            }}\r\n            disabled={isRejecting || isAccepting}\r\n          >\r\n            {isRejecting ? \"Đang từ chối...\" : \"Từ chối\"}\r\n          </Button>\r\n          <Button\r\n            className=\"flex-1 bg-blue-500 hover:bg-blue-600 text-white text-sm h-10 font-semibold\"\r\n            onClick={(e) => {\r\n              e.stopPropagation();\r\n              handleAccept();\r\n            }}\r\n            disabled={isAccepting || isRejecting}\r\n          >\r\n            {isAccepting ? \"Đang chấp nhận...\" : \"Đồng ý\"}\r\n          </Button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Profile Dialog */}\r\n      {showProfileDialog && userData && (\r\n        <ProfileDialog\r\n          isOpen={showProfileDialog}\r\n          onOpenChange={(open) => setShowProfileDialog(open)}\r\n          user={userData}\r\n          isOwnProfile={false}\r\n          onChat={() => {\r\n            // Xử lý khi nhấn nút nhắn tin\r\n            setShowProfileDialog(false);\r\n          }}\r\n          onCall={() => {\r\n            // Xử lý khi nhấn nút gọi điện\r\n            setShowProfileDialog(false);\r\n          }}\r\n        />\r\n      )}\r\n    </>\r\n  );\r\n}\r\n\r\nfunction SentRequestItem({\r\n  id,\r\n  fullName,\r\n  profilePictureUrl,\r\n  timeAgo,\r\n  userData,\r\n}: SentRequestProps) {\r\n  const [isCanceling, setIsCanceling] = useState(false);\r\n  const [showProfileDialog, setShowProfileDialog] = useState(false);\r\n  const { cancelRequest } = useFriendStore();\r\n\r\n  const handleCancel = async () => {\r\n    setIsCanceling(true);\r\n    try {\r\n      const success = await cancelRequest(id);\r\n      if (success) {\r\n        toast.success(`Đã thu hồi lời mời kết bạn đến ${fullName}`);\r\n      } else {\r\n        toast.error(\"Không thể thu hồi lời mời kết bạn. Vui lòng thử lại sau.\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error canceling friend request:\", error);\r\n      toast.error(\"Đã xảy ra lỗi khi thu hồi lời mời kết bạn\");\r\n    } finally {\r\n      setIsCanceling(false);\r\n    }\r\n  };\r\n\r\n  // Hàm xử lý khi nhấn vào card để xem profile\r\n  const handleViewProfile = () => {\r\n    // Log để kiểm tra userData\r\n    console.log(\"userData in SentRequestItem:\", userData);\r\n\r\n    // Nếu có userData (thông tin đầy đủ của người dùng), sử dụng nó\r\n    if (userData) {\r\n      // Hiển thị dialog profile với thông tin đầy đủ\r\n      setShowProfileDialog(true);\r\n    } else {\r\n      // Nếu không có userData, hiển thị thông báo\r\n      console.error(\"userData is undefined, cannot show profile dialog\");\r\n      toast.error(\"Không thể hiển thị thông tin người dùng\");\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <div className=\"bg-white rounded-md p-4 mb-4 w-[302px] shadow-sm\">\r\n        <div\r\n          className=\"flex items-start justify-between mb-2 cursor-pointer\"\r\n          onClick={handleViewProfile}\r\n        >\r\n          <div className=\"flex items-center\">\r\n            <Avatar className=\"h-10 w-10 mr-3 flex-shrink-0\">\r\n              {profilePictureUrl && profilePictureUrl !== \"\" && (\r\n                <AvatarImage\r\n                  src={profilePictureUrl}\r\n                  alt={fullName}\r\n                  className=\"object-cover\"\r\n                />\r\n              )}\r\n              <AvatarFallback className=\"text-sm font-medium bg-gray-200 text-gray-700\">\r\n                {fullName.charAt(0).toUpperCase()}\r\n              </AvatarFallback>\r\n            </Avatar>\r\n            <div>\r\n              <div className=\"font-semibold text-sm\">{fullName}</div>\r\n              <div className=\"text-xs text-gray-500\">{timeAgo}</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"mt-4\">\r\n          <Button\r\n            variant=\"outline\"\r\n            className=\"w-full bg-[#e5e7eb] hover:bg-gray-300 border-gray-200 text-sm h-10 font-semibold\"\r\n            onClick={(e) => {\r\n              e.stopPropagation();\r\n              handleCancel();\r\n            }}\r\n            disabled={isCanceling}\r\n          >\r\n            {isCanceling ? \"Đang thu hồi...\" : \"Thu hồi lời mời\"}\r\n          </Button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Profile Dialog */}\r\n      {showProfileDialog && userData && (\r\n        <ProfileDialog\r\n          isOpen={showProfileDialog}\r\n          onOpenChange={(open) => setShowProfileDialog(open)}\r\n          user={userData}\r\n          isOwnProfile={false}\r\n          onChat={() => {\r\n            // Xử lý khi nhấn nút nhắn tin\r\n            setShowProfileDialog(false);\r\n          }}\r\n          onCall={() => {\r\n            // Xử lý khi nhấn nút gọi điện\r\n            setShowProfileDialog(false);\r\n          }}\r\n        />\r\n      )}\r\n    </>\r\n  );\r\n}\r\n\r\ntype FriendRequestsProps = {\r\n  receivedRequests: FriendRequestProps[];\r\n  sentRequests: SentRequestProps[];\r\n};\r\n\r\nfunction FriendRequests({\r\n  receivedRequests,\r\n  sentRequests,\r\n}: FriendRequestsProps) {\r\n  return (\r\n    <div className=\"space-y-6 overflow-auto no-scrollbar\">\r\n      {receivedRequests.length > 0 && (\r\n        <div>\r\n          <div className=\"flex flex-wrap gap-4 overflow-auto no-scrollbar\">\r\n            {receivedRequests.map((request) => (\r\n              <FriendRequestItem\r\n                key={request.id}\r\n                id={request.id}\r\n                fullName={request.fullName}\r\n                profilePictureUrl={request.profilePictureUrl}\r\n                message={request.message}\r\n                timeAgo={request.timeAgo}\r\n                userData={request.userData}\r\n              />\r\n            ))}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {sentRequests.length > 0 && (\r\n        <div>\r\n          <h2 className=\"text-sm font-semibold mb-3\">\r\n            Lời mời đã gửi ({sentRequests.length})\r\n          </h2>\r\n          <div className=\"flex flex-wrap gap-4 overflow-auto no-scrollbar\">\r\n            {sentRequests.map((request) => (\r\n              <SentRequestItem\r\n                key={request.id}\r\n                id={request.id}\r\n                fullName={request.fullName}\r\n                profilePictureUrl={request.profilePictureUrl}\r\n                timeAgo={request.timeAgo}\r\n                userData={request.userData}\r\n              />\r\n            ))}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {receivedRequests.length === 0 && sentRequests.length === 0 && (\r\n        <div className=\"flex flex-col items-center justify-center py-16\">\r\n          <div className=\"mb-4\">\r\n            <Image\r\n              src=\"/mailbox.png\"\r\n              alt=\"No friend requests\"\r\n              width={120}\r\n              height={120}\r\n            />\r\n          </div>\r\n          <p className=\"text-gray-500 text-center\">Bạn không có lời mời nào</p>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default memo(FriendRequests);\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;;AA2BA,SAAS,kBAAkB,EACzB,EAAE,EACF,QAAQ,EACR,iBAAiB,EACjB,OAAO,EACP,OAAO,EACP,QAAQ,EACW;IACnB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,EAAE,aAAa,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD;IAEtD,MAAM,eAAe;QACnB,eAAe;QACf,IAAI;YACF,MAAM,UAAU,MAAM,cAAc;YACpC,IAAI,SAAS;gBACX,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,gCAAgC,EAAE,UAAU;YAC7D,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CACT;YAEJ;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,eAAe;QACnB,eAAe;QACf,IAAI;YACF,MAAM,UAAU,MAAM,cAAc;YACpC,IAAI,SAAS;gBACX,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,8BAA8B,EAAE,UAAU;YAC3D,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,eAAe;QACjB;IACF;IAEA,6CAA6C;IAC7C,MAAM,oBAAoB;QACxB,2BAA2B;QAC3B,QAAQ,GAAG,CAAC,kCAAkC;QAE9C,gEAAgE;QAChE,IAAI,UAAU;YACZ,+CAA+C;YAC/C,qBAAqB;QACvB,OAAO;YACL,4CAA4C;YAC5C,QAAQ,KAAK,CAAC;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,qBACE;;0BACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,WAAU;wBACV,SAAS;;0CAET,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,WAAU;;4CACf,qBAAqB,sBAAsB,oBAC1C,8OAAC,kIAAA,CAAA,cAAW;gDACV,KAAK;gDACL,KAAK;gDACL,WAAU;;;;;;0DAGd,8OAAC,kIAAA,CAAA,iBAAc;gDAAC,WAAU;0DACvB,SAAS,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;;kDAGnC,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;0DAAyB;;;;;;0DACxC,8OAAC;gDAAI,WAAU;0DAAyB;;;;;;;;;;;;;;;;;;0CAG5C,8OAAC;gCACC,WAAU;gCACV,SAAS,CAAC;oCACR,EAAE,eAAe,IAAI,qCAAqC;oCAC1D;gCACF;0CAEA,cAAA,8OAAC;oCACC,OAAM;oCACN,OAAM;oCACN,QAAO;oCACP,SAAQ;oCACR,MAAK;oCACL,QAAO;oCACP,aAAY;oCACZ,eAAc;oCACd,gBAAe;;sDAEf,8OAAC;4CAAK,GAAE;;;;;;sDACR,8OAAC;4CAAS,QAAO;;;;;;;;;;;;;;;;;;;;;;;kCAKvB,8OAAC;wBACC,WAAU;wBACV,SAAS;kCAER,QAAQ,MAAM,GAAG,MAAM,GAAG,QAAQ,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG;;;;;;kCAG9D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,WAAU;gCACV,SAAS,CAAC;oCACR,EAAE,eAAe;oCACjB;gCACF;gCACA,UAAU,eAAe;0CAExB,cAAc,oBAAoB;;;;;;0CAErC,8OAAC,kIAAA,CAAA,SAAM;gCACL,WAAU;gCACV,SAAS,CAAC;oCACR,EAAE,eAAe;oCACjB;gCACF;gCACA,UAAU,eAAe;0CAExB,cAAc,sBAAsB;;;;;;;;;;;;;;;;;;YAM1C,qBAAqB,0BACpB,8OAAC,8IAAA,CAAA,UAAa;gBACZ,QAAQ;gBACR,cAAc,CAAC,OAAS,qBAAqB;gBAC7C,MAAM;gBACN,cAAc;gBACd,QAAQ;oBACN,8BAA8B;oBAC9B,qBAAqB;gBACvB;gBACA,QAAQ;oBACN,8BAA8B;oBAC9B,qBAAqB;gBACvB;;;;;;;;AAKV;AAEA,SAAS,gBAAgB,EACvB,EAAE,EACF,QAAQ,EACR,iBAAiB,EACjB,OAAO,EACP,QAAQ,EACS;IACjB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD;IAEvC,MAAM,eAAe;QACnB,eAAe;QACf,IAAI;YACF,MAAM,UAAU,MAAM,cAAc;YACpC,IAAI,SAAS;gBACX,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,+BAA+B,EAAE,UAAU;YAC5D,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,eAAe;QACjB;IACF;IAEA,6CAA6C;IAC7C,MAAM,oBAAoB;QACxB,2BAA2B;QAC3B,QAAQ,GAAG,CAAC,gCAAgC;QAE5C,gEAAgE;QAChE,IAAI,UAAU;YACZ,+CAA+C;YAC/C,qBAAqB;QACvB,OAAO;YACL,4CAA4C;YAC5C,QAAQ,KAAK,CAAC;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,qBACE;;0BACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,WAAU;wBACV,SAAS;kCAET,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,WAAU;;wCACf,qBAAqB,sBAAsB,oBAC1C,8OAAC,kIAAA,CAAA,cAAW;4CACV,KAAK;4CACL,KAAK;4CACL,WAAU;;;;;;sDAGd,8OAAC,kIAAA,CAAA,iBAAc;4CAAC,WAAU;sDACvB,SAAS,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;;8CAGnC,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAAyB;;;;;;sDACxC,8OAAC;4CAAI,WAAU;sDAAyB;;;;;;;;;;;;;;;;;;;;;;;kCAK9C,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,WAAU;4BACV,SAAS,CAAC;gCACR,EAAE,eAAe;gCACjB;4BACF;4BACA,UAAU;sCAET,cAAc,oBAAoB;;;;;;;;;;;;;;;;;YAMxC,qBAAqB,0BACpB,8OAAC,8IAAA,CAAA,UAAa;gBACZ,QAAQ;gBACR,cAAc,CAAC,OAAS,qBAAqB;gBAC7C,MAAM;gBACN,cAAc;gBACd,QAAQ;oBACN,8BAA8B;oBAC9B,qBAAqB;gBACvB;gBACA,QAAQ;oBACN,8BAA8B;oBAC9B,qBAAqB;gBACvB;;;;;;;;AAKV;AAOA,SAAS,eAAe,EACtB,gBAAgB,EAChB,YAAY,EACQ;IACpB,qBACE,8OAAC;QAAI,WAAU;;YACZ,iBAAiB,MAAM,GAAG,mBACzB,8OAAC;0BACC,cAAA,8OAAC;oBAAI,WAAU;8BACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,8OAAC;4BAEC,IAAI,QAAQ,EAAE;4BACd,UAAU,QAAQ,QAAQ;4BAC1B,mBAAmB,QAAQ,iBAAiB;4BAC5C,SAAS,QAAQ,OAAO;4BACxB,SAAS,QAAQ,OAAO;4BACxB,UAAU,QAAQ,QAAQ;2BANrB,QAAQ,EAAE;;;;;;;;;;;;;;;YAaxB,aAAa,MAAM,GAAG,mBACrB,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;;4BAA6B;4BACxB,aAAa,MAAM;4BAAC;;;;;;;kCAEvC,8OAAC;wBAAI,WAAU;kCACZ,aAAa,GAAG,CAAC,CAAC,wBACjB,8OAAC;gCAEC,IAAI,QAAQ,EAAE;gCACd,UAAU,QAAQ,QAAQ;gCAC1B,mBAAmB,QAAQ,iBAAiB;gCAC5C,SAAS,QAAQ,OAAO;gCACxB,UAAU,QAAQ,QAAQ;+BALrB,QAAQ,EAAE;;;;;;;;;;;;;;;;YAYxB,iBAAiB,MAAM,KAAK,KAAK,aAAa,MAAM,KAAK,mBACxD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4BACJ,KAAI;4BACJ,KAAI;4BACJ,OAAO;4BACP,QAAQ;;;;;;;;;;;kCAGZ,8OAAC;wBAAE,WAAU;kCAA4B;;;;;;;;;;;;;;;;;;AAKnD;qDAEe,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 1741, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/hooks/useDebounce.ts"], "sourcesContent": ["import { useState, useEffect } from \"react\";\r\n\r\n/**\r\n * A hook that debounces a value\r\n * @param value The value to debounce\r\n * @param delay The delay in milliseconds\r\n * @returns The debounced value\r\n */\r\nexport function useDebounce<T>(value: T, delay: number): T {\r\n  const [debouncedValue, setDebouncedValue] = useState<T>(value);\r\n\r\n  useEffect(() => {\r\n    // Update debounced value after delay\r\n    const handler = setTimeout(() => {\r\n      setDebouncedValue(value);\r\n    }, delay);\r\n\r\n    // Cancel the timeout if value changes or component unmounts\r\n    return () => {\r\n      clearTimeout(handler);\r\n    };\r\n  }, [value, delay]);\r\n\r\n  return debouncedValue;\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AAQO,SAAS,YAAe,KAAQ,EAAE,KAAa;IACpD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAK;IAExD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,qCAAqC;QACrC,MAAM,UAAU,WAAW;YACzB,kBAAkB;QACpB,GAAG;QAEH,4DAA4D;QAC5D,OAAO;YACL,aAAa;QACf;IACF,GAAG;QAAC;QAAO;KAAM;IAEjB,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1769, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/QRCodeDialog.tsx"], "sourcesContent": ["\"use client\";\r\nimport { <PERSON>alog, DialogContent, DialogTitle } from \"@/components/ui/dialog\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { X, Download } from \"lucide-react\";\r\nimport { VisuallyHidden } from \"@radix-ui/react-visually-hidden\";\r\nimport { QRCodeSVG } from \"qrcode.react\";\r\nimport { useRef } from \"react\";\r\n\r\ninterface QRCodeDialogProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  userId: string;\r\n}\r\n\r\nexport default function QRCodeDialog({\r\n  isOpen,\r\n  onClose,\r\n  userId,\r\n}: QRCodeDialogProps) {\r\n  const qrCodeValue = `friend-${userId}`;\r\n  const qrRef = useRef<SVGSVGElement>(null);\r\n\r\n  // Hàm tải xuống mã QR dưới dạng hình ảnh PNG\r\n  const downloadQRCode = () => {\r\n    if (!qrRef.current) return;\r\n\r\n    try {\r\n      // Tạo một canvas từ SVG\r\n      const svg = qrRef.current;\r\n      const canvas = document.createElement(\"canvas\");\r\n      const ctx = canvas.getContext(\"2d\");\r\n      const svgData = new XMLSerializer().serializeToString(svg);\r\n      const img = new Image();\r\n\r\n      // Thiết lập kích thước canvas\r\n      canvas.width = 300;\r\n      canvas.height = 300;\r\n\r\n      // Tạo Blob từ SVG\r\n      const svgBlob = new Blob([svgData], {\r\n        type: \"image/svg+xml;charset=utf-8\",\r\n      });\r\n      const url = URL.createObjectURL(svgBlob);\r\n\r\n      img.onload = () => {\r\n        if (!ctx) return;\r\n\r\n        // Vẽ hình ảnh lên canvas\r\n        ctx.fillStyle = \"white\";\r\n        ctx.fillRect(0, 0, canvas.width, canvas.height);\r\n        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);\r\n        URL.revokeObjectURL(url);\r\n\r\n        // Chuyển đổi canvas thành URL và tải xuống\r\n        canvas.toBlob((blob) => {\r\n          if (!blob) return;\r\n\r\n          const downloadUrl = URL.createObjectURL(blob);\r\n          const a = document.createElement(\"a\");\r\n          a.href = downloadUrl;\r\n          a.download = `qrcode-${userId}.png`;\r\n          document.body.appendChild(a);\r\n          a.click();\r\n          document.body.removeChild(a);\r\n          URL.revokeObjectURL(downloadUrl);\r\n        }, \"image/png\");\r\n      };\r\n\r\n      img.src = url;\r\n    } catch (error) {\r\n      console.error(\"Error downloading QR code:\", error);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>\r\n      <DialogContent className=\"max-w-[400px] p-0 overflow-hidden bg-white border-none\">\r\n        <VisuallyHidden>\r\n          <DialogTitle>Mã QR kết bạn</DialogTitle>\r\n        </VisuallyHidden>\r\n        <div className=\"relative w-full flex flex-col items-center justify-center p-6\">\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            className=\"absolute top-2 right-2 z-10\"\r\n            onClick={onClose}\r\n          >\r\n            <X className=\"h-5 w-5\" />\r\n          </Button>\r\n\r\n          <h2 className=\"text-xl font-semibold mb-4\">Mã QR kết bạn</h2>\r\n          <p className=\"text-sm text-gray-500 mb-6 text-center\">\r\n            Chia sẻ mã QR này để người khác có thể quét và kết bạn với bạn\r\n          </p>\r\n\r\n          <div className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-200 mb-4\">\r\n            <div className=\"w-[250px] h-[250px] flex items-center justify-center\">\r\n              <QRCodeSVG\r\n                value={qrCodeValue}\r\n                size={250}\r\n                level=\"L\"\r\n                includeMargin={true}\r\n                ref={qrRef}\r\n                bgColor=\"#FFFFFF\"\r\n                fgColor=\"#000000\"\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          <Button\r\n            variant=\"outline\"\r\n            onClick={downloadQRCode}\r\n            className=\"mb-4 flex items-center gap-2\"\r\n          >\r\n            <Download className=\"h-4 w-4\" />\r\n            Tải mã QR\r\n          </Button>\r\n\r\n          <div className=\"text-sm text-center font-medium text-gray-700 mt-2\">\r\n            Quét mã để kết bạn\r\n          </div>\r\n        </div>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AANA;;;;;;;;AAce,SAAS,aAAa,EACnC,MAAM,EACN,OAAO,EACP,MAAM,EACY;IAClB,MAAM,cAAc,CAAC,OAAO,EAAE,QAAQ;IACtC,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAiB;IAEpC,6CAA6C;IAC7C,MAAM,iBAAiB;QACrB,IAAI,CAAC,MAAM,OAAO,EAAE;QAEpB,IAAI;YACF,wBAAwB;YACxB,MAAM,MAAM,MAAM,OAAO;YACzB,MAAM,SAAS,SAAS,aAAa,CAAC;YACtC,MAAM,MAAM,OAAO,UAAU,CAAC;YAC9B,MAAM,UAAU,IAAI,gBAAgB,iBAAiB,CAAC;YACtD,MAAM,MAAM,IAAI;YAEhB,8BAA8B;YAC9B,OAAO,KAAK,GAAG;YACf,OAAO,MAAM,GAAG;YAEhB,kBAAkB;YAClB,MAAM,UAAU,IAAI,KAAK;gBAAC;aAAQ,EAAE;gBAClC,MAAM;YACR;YACA,MAAM,MAAM,IAAI,eAAe,CAAC;YAEhC,IAAI,MAAM,GAAG;gBACX,IAAI,CAAC,KAAK;gBAEV,yBAAyB;gBACzB,IAAI,SAAS,GAAG;gBAChB,IAAI,QAAQ,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;gBAC9C,IAAI,SAAS,CAAC,KAAK,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;gBACpD,IAAI,eAAe,CAAC;gBAEpB,2CAA2C;gBAC3C,OAAO,MAAM,CAAC,CAAC;oBACb,IAAI,CAAC,MAAM;oBAEX,MAAM,cAAc,IAAI,eAAe,CAAC;oBACxC,MAAM,IAAI,SAAS,aAAa,CAAC;oBACjC,EAAE,IAAI,GAAG;oBACT,EAAE,QAAQ,GAAG,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC;oBACnC,SAAS,IAAI,CAAC,WAAW,CAAC;oBAC1B,EAAE,KAAK;oBACP,SAAS,IAAI,CAAC,WAAW,CAAC;oBAC1B,IAAI,eAAe,CAAC;gBACtB,GAAG;YACL;YAEA,IAAI,GAAG,GAAG;QACZ,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc,CAAC,OAAS,CAAC,QAAQ;kBACrD,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,8KAAA,CAAA,iBAAc;8BACb,cAAA,8OAAC,kIAAA,CAAA,cAAW;kCAAC;;;;;;;;;;;8BAEf,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,SAAS;sCAET,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;sCAGf,8OAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAC3C,8OAAC;4BAAE,WAAU;sCAAyC;;;;;;sCAItD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,sJAAA,CAAA,YAAS;oCACR,OAAO;oCACP,MAAM;oCACN,OAAM;oCACN,eAAe;oCACf,KAAK;oCACL,SAAQ;oCACR,SAAQ;;;;;;;;;;;;;;;;sCAKd,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS;4BACT,WAAU;;8CAEV,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAIlC,8OAAC;4BAAI,WAAU;sCAAqD;;;;;;;;;;;;;;;;;;;;;;;AAO9E", "debugId": null}}, {"offset": {"line": 1969, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\";\r\nimport { Check } from \"lucide-react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst Checkbox = React.forwardRef<\r\n  React.ElementRef<typeof CheckboxPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <CheckboxPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\r\n      \"peer h-4 w-4 shrink-0 rounded-sm border border-primary shadow focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  >\r\n    <CheckboxPrimitive.Indicator\r\n      className={cn(\"flex items-center justify-center text-current\")}\r\n    >\r\n      <Check className=\"h-4 w-4\" />\r\n    </CheckboxPrimitive.Indicator>\r\n  </CheckboxPrimitive.Root>\r\n));\r\nCheckbox.displayName = CheckboxPrimitive.Root.displayName;\r\n\r\nexport { Checkbox };\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,oKAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sQACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE;sBAEd,cAAA,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;;;;;;;;;;;AAIvB,SAAS,WAAW,GAAG,oKAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2014, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/group/CreateGroupDialog.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect, use<PERSON><PERSON>back, useMemo } from \"react\";\r\nimport {\r\n  <PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON>ontent,\r\n  Di<PERSON>Header,\r\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\r\n  DialogFooter,\r\n} from \"@/components/ui/dialog\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport { Users, Upload, Search } from \"lucide-react\";\r\nimport { useAuthStore } from \"@/stores/authStore\";\r\nimport { createGroupWithAvatar } from \"@/actions/group.action\";\r\nimport { toast } from \"sonner\";\r\nimport { useFriendStore } from \"@/stores/friendStore\";\r\nimport { useConversationsStore } from \"@/stores/conversationsStore\";\r\nimport { Checkbox } from \"@/components/ui/checkbox\";\r\nimport { ScrollArea } from \"@/components/ui/scroll-area\";\r\nimport { isEmail, isPhoneNumber } from \"@/utils/helpers\";\r\n\r\ninterface CreateGroupDialogProps {\r\n  isOpen: boolean;\r\n  onOpenChange: (open: boolean) => void;\r\n  preSelectedFriendId?: string;\r\n}\r\n\r\nexport default function CreateGroupDialog({\r\n  isOpen,\r\n  onOpenChange,\r\n  preSelectedFriendId,\r\n}: CreateGroupDialogProps) {\r\n  const [groupName, setGroupName] = useState(\"\");\r\n  const [selectedFriends, setSelectedFriends] = useState<string[]>([]);\r\n  const [avatarFile, setAvatarFile] = useState<File | null>(null);\r\n  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n\r\n  const { user: currentUser } = useAuthStore();\r\n  const { friends } = useFriendStore();\r\n\r\n  // Memoize callback functions to prevent unnecessary re-renders\r\n  const handleAvatarChange = useCallback(\r\n    (e: React.ChangeEvent<HTMLInputElement>) => {\r\n      const file = e.target.files?.[0];\r\n      if (file) {\r\n        setAvatarFile(file);\r\n        const reader = new FileReader();\r\n        reader.onloadend = () => {\r\n          setAvatarPreview(reader.result as string);\r\n        };\r\n        reader.readAsDataURL(file);\r\n      }\r\n    },\r\n    [],\r\n  );\r\n\r\n  const handleFriendSelection = useCallback((friendId: string) => {\r\n    setSelectedFriends((prev) =>\r\n      prev.includes(friendId)\r\n        ? prev.filter((id) => id !== friendId)\r\n        : [...prev, friendId],\r\n    );\r\n  }, []);\r\n\r\n  const handleCreateGroup = useCallback(async () => {\r\n    if (!groupName.trim()) {\r\n      toast.error(\"Vui lòng nhập tên nhóm\");\r\n      return;\r\n    }\r\n\r\n    if (selectedFriends.length < 2) {\r\n      toast.error(\r\n        \"Vui lòng chọn ít nhất 2 thành viên (nhóm phải có tối thiểu 3 người kể cả bạn)\",\r\n      );\r\n      return;\r\n    }\r\n\r\n    setIsLoading(true);\r\n    try {\r\n      // Chuyển đổi danh sách ID thành viên thành định dạng mới\r\n      const initialMembers = selectedFriends.map((userId) => ({\r\n        userId: userId,\r\n        // addedById sẽ được thêm tự động trong createGroupWithAvatar\r\n      }));\r\n\r\n      // Kiểm tra currentUser có tồn tại không\r\n      if (!currentUser || !currentUser.id) {\r\n        toast.error(\"Bạn cần đăng nhập để tạo nhóm\");\r\n        setIsLoading(false);\r\n        return;\r\n      }\r\n\r\n      // Gọi API tạo nhóm với avatar trong một lần gọi duy nhất\r\n      const result = await createGroupWithAvatar(\r\n        groupName.trim(),\r\n        currentUser.id,\r\n        initialMembers,\r\n        avatarFile || undefined,\r\n      );\r\n\r\n      if (result.success && result.group) {\r\n        // Đóng dialog trước\r\n        onOpenChange(false);\r\n\r\n        // Thông báo thành công\r\n        toast.success(\"Tạo nhóm thành công\");\r\n\r\n        // Reset form\r\n        setGroupName(\"\");\r\n        setSelectedFriends([]);\r\n        setAvatarFile(null);\r\n        setAvatarPreview(null);\r\n\r\n        // Backend sẽ tự động gửi socket events cho tất cả thành viên\r\n        // Chỉ cần reload conversations để cập nhật UI\r\n        if (currentUser?.id) {\r\n          console.log(\"Group created successfully:\", {\r\n            id: result.group.id,\r\n            name: result.group.name,\r\n            type: \"GROUP\",\r\n          });\r\n\r\n          // Reload conversations sau khi tạo nhóm thành công\r\n          setTimeout(() => {\r\n            const conversationsStore = useConversationsStore.getState();\r\n            conversationsStore.loadConversations(currentUser.id);\r\n          }, 500);\r\n        }\r\n      } else {\r\n        toast.error(result.error || \"Không thể tạo nhóm\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error creating group:\", error);\r\n      toast.error(\"Đã xảy ra lỗi khi tạo nhóm\");\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, [groupName, selectedFriends, avatarFile, currentUser, onOpenChange]);\r\n\r\n  // State for search query\r\n  const [searchQuery, setSearchQuery] = useState(\"\");\r\n\r\n  // Pre-select friend if provided and reset when dialog opens/closes\r\n  useEffect(() => {\r\n    // Reset selected friends when dialog opens/closes\r\n    setSelectedFriends([]);\r\n\r\n    // Only add preSelectedFriendId if dialog is open\r\n    if (\r\n      isOpen &&\r\n      preSelectedFriendId &&\r\n      friends.some((friend) => friend.id === preSelectedFriendId)\r\n    ) {\r\n      setSelectedFriends([preSelectedFriendId]);\r\n    }\r\n  }, [isOpen, preSelectedFriendId, friends]);\r\n\r\n  // Use useMemo for filtered friends to avoid recalculating on every render\r\n  const filteredFriends = useMemo(() => {\r\n    if (!searchQuery.trim()) {\r\n      return friends;\r\n    }\r\n\r\n    // Check if search query is a phone number or email\r\n    const isPhone = isPhoneNumber(searchQuery);\r\n    const isEmailValue = isEmail(searchQuery);\r\n\r\n    // Filter friends based on search query\r\n    return friends.filter((friend) => {\r\n      // Search by phone number\r\n      if (isPhone && friend.phoneNumber) {\r\n        return friend.phoneNumber.includes(searchQuery);\r\n      }\r\n      // Search by email\r\n      if (isEmailValue && friend.email) {\r\n        return friend.email.toLowerCase().includes(searchQuery.toLowerCase());\r\n      }\r\n      // Search by name (default)\r\n      return friend.fullName.toLowerCase().includes(searchQuery.toLowerCase());\r\n    });\r\n  }, [searchQuery, friends]);\r\n\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={onOpenChange}>\r\n      <DialogContent className=\"sm:max-w-[500px]\">\r\n        <DialogHeader>\r\n          <DialogTitle className=\"text-center text-lg font-semibold\">\r\n            Tạo nhóm\r\n          </DialogTitle>\r\n        </DialogHeader>\r\n\r\n        <div className=\"space-y-4 py-2\">\r\n          {/* Group avatar upload */}\r\n          <div className=\"flex flex-row w-full items-end justify-center\">\r\n            <div className=\"relative\">\r\n              <Avatar className=\"h-12 w-12 cursor-pointer\">\r\n                {avatarPreview ? (\r\n                  <AvatarImage src={avatarPreview} alt=\"Group avatar\" />\r\n                ) : (\r\n                  <>\r\n                    <AvatarFallback className=\"bg-gray-200\">\r\n                      <Users className=\"h-8 w-8 text-gray-400\" />\r\n                    </AvatarFallback>\r\n                  </>\r\n                )}\r\n              </Avatar>\r\n              <label\r\n                htmlFor=\"avatar-upload\"\r\n                className=\"absolute bottom-0 right-0 bg-blue-500 text-white p-1 rounded-full cursor-pointer\"\r\n              >\r\n                <Upload className=\"h-4 w-4\" />\r\n                <input\r\n                  id=\"avatar-upload\"\r\n                  type=\"file\"\r\n                  accept=\"image/*\"\r\n                  className=\"hidden\"\r\n                  onChange={handleAvatarChange}\r\n                />\r\n              </label>\r\n            </div>\r\n            {/* Group name input */}\r\n            <div className=\"ml-2 w-full border-b\">\r\n              <Input\r\n                value={groupName}\r\n                onChange={(e) => setGroupName(e.target.value)}\r\n                placeholder=\"Nhập tên nhóm...\"\r\n                className=\"w-full !border-none focus-visible:ring-0 focus-visible:ring-offset-0\"\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          {/* Search input */}\r\n          <div className=\"relative\">\r\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n              <Search className=\"h-4 w-4 text-gray-400\" />\r\n            </div>\r\n            <Input\r\n              type=\"text\"\r\n              placeholder=\"Nhập tên, số điện thoại, hoặc danh sách số điện thoại\"\r\n              className=\"pl-10 w-full text-xs\"\r\n              value={searchQuery}\r\n              onChange={(e) => setSearchQuery(e.target.value)}\r\n            />\r\n          </div>\r\n\r\n          {/* Friend selection */}\r\n          <div>\r\n            <div className=\"border rounded-md\">\r\n              <div className=\"p-2 border-b flex items-center justify-between\">\r\n                <span className=\"text-sm font-medium\">Trò chuyện gần đây</span>\r\n                <span className=\"text-sm text-blue-500\">\r\n                  Đã chọn: {selectedFriends.length}{\" \"}\r\n                  <span className=\"text-xs text-gray-500\">(tối thiểu 2)</span>\r\n                </span>\r\n              </div>\r\n\r\n              <ScrollArea className=\"h-[200px]\">\r\n                {filteredFriends.length > 0 ? (\r\n                  <div>\r\n                    {filteredFriends.map((friend) => (\r\n                      <div\r\n                        key={friend.id}\r\n                        className=\"flex items-center py-2 px-3 hover:bg-gray-50 border-b border-gray-100\"\r\n                      >\r\n                        <div className=\"flex items-center w-full\">\r\n                          <Checkbox\r\n                            id={`friend-${friend.id}`}\r\n                            checked={selectedFriends.includes(friend.id)}\r\n                            onCheckedChange={() =>\r\n                              handleFriendSelection(friend.id)\r\n                            }\r\n                            className=\"mr-3\"\r\n                          />\r\n                          <Avatar className=\"h-10 w-10 mr-3\">\r\n                            <AvatarImage\r\n                              src={friend.profilePictureUrl || undefined}\r\n                              alt={friend.fullName || \"\"}\r\n                            />\r\n                            <AvatarFallback>\r\n                              {friend.fullName?.charAt(0) || \"U\"}\r\n                            </AvatarFallback>\r\n                          </Avatar>\r\n                          <span className=\"text-sm font-medium\">\r\n                            {friend.fullName}\r\n                          </span>\r\n                        </div>\r\n                      </div>\r\n                    ))}\r\n                  </div>\r\n                ) : (\r\n                  <div className=\"p-4 text-center text-gray-500\">\r\n                    <p>Không tìm thấy kết quả</p>\r\n                  </div>\r\n                )}\r\n              </ScrollArea>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <DialogFooter>\r\n          <Button\r\n            variant=\"outline\"\r\n            onClick={() => onOpenChange(false)}\r\n            disabled={isLoading}\r\n            className=\"mr-2\"\r\n          >\r\n            Hủy\r\n          </Button>\r\n          <Button\r\n            onClick={handleCreateGroup}\r\n            disabled={\r\n              isLoading || !groupName.trim() || selectedFriends.length < 2\r\n            }\r\n            className=\"bg-blue-500 hover:bg-blue-600\"\r\n          >\r\n            {isLoading ? (\r\n              <>\r\n                <div className=\"h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"></div>\r\n                Đang tạo...\r\n              </>\r\n            ) : (\r\n              \"Tạo nhóm\"\r\n            )}\r\n          </Button>\r\n        </DialogFooter>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAOA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AArBA;;;;;;;;;;;;;;;;AA6Be,SAAS,kBAAkB,EACxC,MAAM,EACN,YAAY,EACZ,mBAAmB,EACI;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC1D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,EAAE,MAAM,WAAW,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD;IACzC,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD;IAEjC,+DAA+D;IAC/D,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACnC,CAAC;QACC,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QAChC,IAAI,MAAM;YACR,cAAc;YACd,MAAM,SAAS,IAAI;YACnB,OAAO,SAAS,GAAG;gBACjB,iBAAiB,OAAO,MAAM;YAChC;YACA,OAAO,aAAa,CAAC;QACvB;IACF,GACA,EAAE;IAGJ,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACzC,mBAAmB,CAAC,OAClB,KAAK,QAAQ,CAAC,YACV,KAAK,MAAM,CAAC,CAAC,KAAO,OAAO,YAC3B;mBAAI;gBAAM;aAAS;IAE3B,GAAG,EAAE;IAEL,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACpC,IAAI,CAAC,UAAU,IAAI,IAAI;YACrB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,gBAAgB,MAAM,GAAG,GAAG;YAC9B,wIAAA,CAAA,QAAK,CAAC,KAAK,CACT;YAEF;QACF;QAEA,aAAa;QACb,IAAI;YACF,yDAAyD;YACzD,MAAM,iBAAiB,gBAAgB,GAAG,CAAC,CAAC,SAAW,CAAC;oBACtD,QAAQ;gBAEV,CAAC;YAED,wCAAwC;YACxC,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,EAAE;gBACnC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,aAAa;gBACb;YACF;YAEA,yDAAyD;YACzD,MAAM,SAAS,MAAM,CAAA,GAAA,iIAAA,CAAA,wBAAqB,AAAD,EACvC,UAAU,IAAI,IACd,YAAY,EAAE,EACd,gBACA,cAAc;YAGhB,IAAI,OAAO,OAAO,IAAI,OAAO,KAAK,EAAE;gBAClC,oBAAoB;gBACpB,aAAa;gBAEb,uBAAuB;gBACvB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAEd,aAAa;gBACb,aAAa;gBACb,mBAAmB,EAAE;gBACrB,cAAc;gBACd,iBAAiB;gBAEjB,6DAA6D;gBAC7D,8CAA8C;gBAC9C,IAAI,aAAa,IAAI;oBACnB,QAAQ,GAAG,CAAC,+BAA+B;wBACzC,IAAI,OAAO,KAAK,CAAC,EAAE;wBACnB,MAAM,OAAO,KAAK,CAAC,IAAI;wBACvB,MAAM;oBACR;oBAEA,mDAAmD;oBACnD,WAAW;wBACT,MAAM,qBAAqB,mIAAA,CAAA,wBAAqB,CAAC,QAAQ;wBACzD,mBAAmB,iBAAiB,CAAC,YAAY,EAAE;oBACrD,GAAG;gBACL;YACF,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,KAAK,IAAI;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF,GAAG;QAAC;QAAW;QAAiB;QAAY;QAAa;KAAa;IAEtE,yBAAyB;IACzB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,mEAAmE;IACnE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,kDAAkD;QAClD,mBAAmB,EAAE;QAErB,iDAAiD;QACjD,IACE,UACA,uBACA,QAAQ,IAAI,CAAC,CAAC,SAAW,OAAO,EAAE,KAAK,sBACvC;YACA,mBAAmB;gBAAC;aAAoB;QAC1C;IACF,GAAG;QAAC;QAAQ;QAAqB;KAAQ;IAEzC,0EAA0E;IAC1E,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC9B,IAAI,CAAC,YAAY,IAAI,IAAI;YACvB,OAAO;QACT;QAEA,mDAAmD;QACnD,MAAM,UAAU,CAAA,GAAA,uHAAA,CAAA,gBAAa,AAAD,EAAE;QAC9B,MAAM,eAAe,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD,EAAE;QAE7B,uCAAuC;QACvC,OAAO,QAAQ,MAAM,CAAC,CAAC;YACrB,yBAAyB;YACzB,IAAI,WAAW,OAAO,WAAW,EAAE;gBACjC,OAAO,OAAO,WAAW,CAAC,QAAQ,CAAC;YACrC;YACA,kBAAkB;YAClB,IAAI,gBAAgB,OAAO,KAAK,EAAE;gBAChC,OAAO,OAAO,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;YACpE;YACA,2BAA2B;YAC3B,OAAO,OAAO,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;QACvE;IACF,GAAG;QAAC;QAAa;KAAQ;IAEzB,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;8BACX,cAAA,8OAAC,kIAAA,CAAA,cAAW;wBAAC,WAAU;kCAAoC;;;;;;;;;;;8BAK7D,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CAAC,WAAU;sDACf,8BACC,8OAAC,kIAAA,CAAA,cAAW;gDAAC,KAAK;gDAAe,KAAI;;;;;qEAErC;0DACE,cAAA,8OAAC,kIAAA,CAAA,iBAAc;oDAAC,WAAU;8DACxB,cAAA,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAKzB,8OAAC;4CACC,SAAQ;4CACR,WAAU;;8DAEV,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,QAAO;oDACP,WAAU;oDACV,UAAU;;;;;;;;;;;;;;;;;;8CAKhB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;wCACJ,OAAO;wCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;wCAC5C,aAAY;wCACZ,WAAU;;;;;;;;;;;;;;;;;sCAMhB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;8CAEpB,8OAAC,iIAAA,CAAA,QAAK;oCACJ,MAAK;oCACL,aAAY;oCACZ,WAAU;oCACV,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;sCAKlD,8OAAC;sCACC,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAsB;;;;;;0DACtC,8OAAC;gDAAK,WAAU;;oDAAwB;oDAC5B,gBAAgB,MAAM;oDAAE;kEAClC,8OAAC;wDAAK,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;kDAI5C,8OAAC,0IAAA,CAAA,aAAU;wCAAC,WAAU;kDACnB,gBAAgB,MAAM,GAAG,kBACxB,8OAAC;sDACE,gBAAgB,GAAG,CAAC,CAAC,uBACpB,8OAAC;oDAEC,WAAU;8DAEV,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oIAAA,CAAA,WAAQ;gEACP,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;gEACzB,SAAS,gBAAgB,QAAQ,CAAC,OAAO,EAAE;gEAC3C,iBAAiB,IACf,sBAAsB,OAAO,EAAE;gEAEjC,WAAU;;;;;;0EAEZ,8OAAC,kIAAA,CAAA,SAAM;gEAAC,WAAU;;kFAChB,8OAAC,kIAAA,CAAA,cAAW;wEACV,KAAK,OAAO,iBAAiB,IAAI;wEACjC,KAAK,OAAO,QAAQ,IAAI;;;;;;kFAE1B,8OAAC,kIAAA,CAAA,iBAAc;kFACZ,OAAO,QAAQ,EAAE,OAAO,MAAM;;;;;;;;;;;;0EAGnC,8OAAC;gEAAK,WAAU;0EACb,OAAO,QAAQ;;;;;;;;;;;;mDAtBf,OAAO,EAAE;;;;;;;;;iEA6BpB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;0DAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQf,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS,IAAM,aAAa;4BAC5B,UAAU;4BACV,WAAU;sCACX;;;;;;sCAGD,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UACE,aAAa,CAAC,UAAU,IAAI,MAAM,gBAAgB,MAAM,GAAG;4BAE7D,WAAU;sCAET,0BACC;;kDACE,8OAAC;wCAAI,WAAU;;;;;;oCAA0F;;+CAI3G;;;;;;;;;;;;;;;;;;;;;;;AAOd", "debugId": null}}, {"offset": {"line": 2531, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/SearchHeader.tsx"], "sourcesContent": ["\"use client\";\r\nimport {\r\n  Search,\r\n  UserPlus,\r\n  Users,\r\n  X,\r\n  MoreH<PERSON>zon<PERSON>,\r\n  Clock,\r\n  Bell,\r\n  SmilePlus,\r\n} from \"lucide-react\";\r\nimport { useState, useEffect, useRef } from \"react\";\r\nimport Image from \"next/image\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { useDebounce } from \"@/hooks/useDebounce\";\r\nimport { searchUser, getUserDataById } from \"@/actions/user.action\";\r\nimport { searchMessagesGlobal } from \"@/actions/message.action\";\r\nimport { getFriendsList } from \"@/actions/friend.action\";\r\nimport { isEmail, isPhoneNumber } from \"@/utils/helpers\";\r\nimport { useAuthStore } from \"@/stores/authStore\";\r\nimport { useChatStore } from \"@/stores/chatStore\";\r\nimport { User, Message } from \"@/types/base\";\r\nimport { toast } from \"sonner\";\r\n\r\nimport ProfileDialog from \"./profile/ProfileDialog\";\r\nimport QRCodeDialog from \"./QRCodeDialog\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport CreateGroupDialog from \"./group/CreateGroupDialog\";\r\n\r\n// Extended Message type with search context\r\ninterface MessageWithContext extends Message {\r\n  _searchContext?: {\r\n    userId: string;\r\n  };\r\n}\r\n\r\n// Sử dụng type đơn giản hóa cho Friend\r\ntype Friend = {\r\n  id: string;\r\n  fullName: string;\r\n  profilePictureUrl: string;\r\n  phoneNumber?: string;\r\n  email?: string;\r\n};\r\n\r\n// Type cho kết quả tìm kiếm tin nhắn\r\ninterface SearchResultMessage {\r\n  id: string;\r\n  sender: {\r\n    id: string;\r\n    fullName: string;\r\n    profilePictureUrl: string;\r\n  };\r\n  content: string;\r\n  conversationName?: string;\r\n  date: string;\r\n  highlighted?: boolean;\r\n  _searchContext?: {\r\n    userId: string;\r\n  };\r\n}\r\n\r\n// Type đơn giản hóa cho kết quả tìm kiếm người dùng\r\ntype UserSearchResult = {\r\n  id: string;\r\n  fullName: string;\r\n  profilePictureUrl: string;\r\n  phoneNumber: string;\r\n  email?: string;\r\n};\r\n\r\nexport default function SearchHeader({ className }: { className?: string }) {\r\n  const [searchQuery, setSearchQuery] = useState<string>(\"\");\r\n  const [showResults, setShowResults] = useState<boolean>(false);\r\n  const [showSuggestions, setShowSuggestions] = useState<boolean>(false);\r\n  const [isSearchActive, setIsSearchActive] = useState<boolean>(false);\r\n  const [filteredFriends, setFilteredFriends] = useState<Friend[]>([]);\r\n  const [allFriends, setAllFriends] = useState<Friend[]>([]);\r\n  const [filteredMessages, setFilteredMessages] = useState<\r\n    SearchResultMessage[]\r\n  >([]);\r\n  // State lưu thông tin người gửi đã được lấy từ API\r\n  const [senderDetails, setSenderDetails] = useState<{ [key: string]: User }>(\r\n    {},\r\n  );\r\n\r\n  const [recentSearches, setRecentSearches] = useState<string[]>([]);\r\n  const [phoneSearchResult, setPhoneSearchResult] =\r\n    useState<UserSearchResult | null>(null);\r\n  const [isSearchingUser, setIsSearchingUser] = useState<boolean>(false);\r\n  const [isLoadingFriends, setIsLoadingFriends] = useState<boolean>(false);\r\n  const [showProfileDialog, setShowProfileDialog] = useState<boolean>(false);\r\n  const [selectedUser, setSelectedUser] = useState<User | null>(null);\r\n  const [isAddFriendMode, setIsAddFriendMode] = useState<boolean>(false);\r\n  const [showQRCodeDialog, setShowQRCodeDialog] = useState<boolean>(false);\r\n  const [showCreateGroupDialog, setShowCreateGroupDialog] =\r\n    useState<boolean>(false);\r\n  const searchRef = useRef<HTMLDivElement>(null);\r\n  const debouncedSearchQuery = useDebounce(searchQuery, 300);\r\n  const { accessToken, user: currentUser } = useAuthStore();\r\n  const { openChat } = useChatStore();\r\n  const router = useRouter();\r\n\r\n  // Lấy danh sách bạn bè khi component mount và người dùng đã đăng nhập\r\n  useEffect(() => {\r\n    const fetchFriends = async () => {\r\n      // Kiểm tra xem người dùng đã đăng nhập hay chưa\r\n      if (!accessToken || !currentUser) {\r\n        setAllFriends([]);\r\n        return;\r\n      }\r\n\r\n      try {\r\n        setIsLoadingFriends(true);\r\n        const result = await getFriendsList(accessToken);\r\n        if (result.success && result.friends) {\r\n          setAllFriends(result.friends);\r\n        } else {\r\n          console.error(\"Failed to fetch friends:\", result.error);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error fetching friends:\", error);\r\n      } finally {\r\n        setIsLoadingFriends(false);\r\n      }\r\n    };\r\n\r\n    fetchFriends();\r\n  }, [accessToken, currentUser]);\r\n\r\n  // Effect để lấy thông tin người gửi từ API khi có kết quả tìm kiếm\r\n  useEffect(() => {\r\n    // Nếu không có tin nhắn hoặc không có token, không làm gì\r\n    if (filteredMessages.length === 0 || !accessToken || !currentUser) {\r\n      return;\r\n    }\r\n\r\n    // Lấy danh sách ID người gửi để lấy thông tin\r\n    // Loại bỏ các ID không hợp lệ ngay từ đầu\r\n    const senderIds = filteredMessages\r\n      .filter((msg) => {\r\n        // Kiểm tra ID hợp lệ và không phải ID hệ thống\r\n        return (\r\n          msg.sender &&\r\n          msg.sender.id &&\r\n          msg.sender.id.trim() !== \"\" &&\r\n          msg.sender.id !== \"system\" &&\r\n          msg.sender.id !== \"unknown\" &&\r\n          msg.sender.id !== \"loading\"\r\n        );\r\n      })\r\n      .map((msg) => msg.sender.id)\r\n      // Lọc các ID trùng lặp\r\n      .filter((id, index, self) => self.indexOf(id) === index);\r\n\r\n    // Nếu không có ID hợp lệ nào, không làm gì\r\n    if (senderIds.length === 0) {\r\n      return;\r\n    }\r\n\r\n    // Lấy thông tin người gửi từ API\r\n    const fetchSenderDetails = async () => {\r\n      for (const senderId of senderIds) {\r\n        // Kiểm tra xem đã có thông tin người gửi trong state chưa\r\n        if (!senderDetails[senderId]) {\r\n          try {\r\n            // Gọi API với ID đã được kiểm tra\r\n            const result = await getUserDataById(senderId);\r\n\r\n            if (result.success && result.user) {\r\n              // Cập nhật thông tin người gửi vào state\r\n              setSenderDetails((prev) => ({\r\n                ...prev,\r\n                [senderId]: result.user,\r\n              }));\r\n            }\r\n          } catch (error) {\r\n            console.error(`Error fetching user data for ${senderId}:`, error);\r\n            // Không làm gì khi có lỗi, để tránh gọi lại API\r\n          }\r\n        }\r\n      }\r\n    };\r\n\r\n    fetchSenderDetails();\r\n  }, [filteredMessages, accessToken, currentUser, senderDetails]);\r\n\r\n  // Handle click outside to close search results and suggestions\r\n  useEffect(() => {\r\n    function handleClickOutside(event: MouseEvent) {\r\n      if (\r\n        searchRef.current &&\r\n        !searchRef.current.contains(event.target as Node)\r\n      ) {\r\n        setShowResults(false);\r\n        setShowSuggestions(false);\r\n        setIsSearchActive(false);\r\n      }\r\n    }\r\n\r\n    document.addEventListener(\"mousedown\", handleClickOutside);\r\n    return () => {\r\n      document.removeEventListener(\"mousedown\", handleClickOutside);\r\n    };\r\n  }, []);\r\n\r\n  // Search function\r\n  useEffect(() => {\r\n    if (!debouncedSearchQuery) {\r\n      setFilteredFriends([]);\r\n      setFilteredMessages([]);\r\n      setPhoneSearchResult(null);\r\n      setIsSearchingUser(false);\r\n      return;\r\n    }\r\n\r\n    // Kiểm tra xem có phải số điện thoại hoặc email không\r\n    const isPhone = isPhoneNumber(debouncedSearchQuery);\r\n    const isEmailValue = isEmail(debouncedSearchQuery);\r\n    const isSearchingUser = isPhone || isEmailValue;\r\n    setIsSearchingUser(isSearchingUser);\r\n\r\n    // Tìm kiếm tin nhắn dựa trên từ khóa\r\n    const searchForMessages = async () => {\r\n      // Kiểm tra xem người dùng đã đăng nhập hay chưa\r\n      if (!accessToken || !currentUser) {\r\n        setFilteredMessages([]);\r\n        return;\r\n      }\r\n\r\n      try {\r\n        console.log(\"Searching messages with query:\", debouncedSearchQuery);\r\n        // Hiển thị trạng thái đang tìm kiếm\r\n        setFilteredMessages([\r\n          {\r\n            id: \"loading\",\r\n            content: \"Đang tìm kiếm tin nhắn...\",\r\n            sender: {\r\n              id: \"system\",\r\n              fullName: \"Hệ thống\",\r\n              profilePictureUrl: \"/images/default-avatar.png\",\r\n            },\r\n            date: new Date().toLocaleDateString(),\r\n            highlighted: false,\r\n          },\r\n        ]);\r\n\r\n        // Lấy danh sách ID của tất cả bạn bè để tìm kiếm tin nhắn\r\n        const friendIds = allFriends.map((friend) => friend.id);\r\n\r\n        // Nếu không có bạn bè nào, không cần tìm kiếm\r\n        if (friendIds.length === 0) {\r\n          setFilteredMessages([]);\r\n          return;\r\n        }\r\n\r\n        // Truyền token và danh sách ID bạn bè vào hàm searchMessagesGlobal\r\n        const result = await searchMessagesGlobal(\r\n          debouncedSearchQuery,\r\n          friendIds,\r\n        );\r\n\r\n        if (result.success && result.messages && result.messages.length > 0) {\r\n          console.log(\"Found messages:\", result.messages.length);\r\n          // Chuyển đổi từ Message từ API sang SearchResultMessage trong component\r\n          const messages: SearchResultMessage[] = result.messages.map(\r\n            (message: MessageWithContext) => {\r\n              try {\r\n                // Xử lý nội dung tin nhắn\r\n                let messageContent = \"\";\r\n                if (typeof message.content === \"string\") {\r\n                  messageContent = message.content;\r\n                } else if (\r\n                  message.content &&\r\n                  typeof message.content === \"object\"\r\n                ) {\r\n                  // Kiểm tra nếu content là object và có thuộc tính text\r\n                  messageContent = message.content.text || \"\";\r\n                }\r\n\r\n                // Sử dụng messageContent để hiển thị nội dung tin nhắn\r\n\r\n                // Xử lý thông tin người gửi dựa trên dữ liệu trả về từ API\r\n                // Dữ liệu trả về có dạng: sender: { id, email, phoneNumber, ... }\r\n\r\n                // Lấy thông tin về cuộc trò chuyện\r\n                let conversationInfo = \"\";\r\n                if (message._searchContext && message._searchContext.userId) {\r\n                  // Tìm tên người dùng từ danh sách bạn bè\r\n                  const friend = allFriends.find(\r\n                    (f) => f.id === message._searchContext?.userId,\r\n                  );\r\n                  if (friend) {\r\n                    conversationInfo = friend.fullName;\r\n                  }\r\n                }\r\n\r\n                // Lưu thông tin người gửi từ API response\r\n                return {\r\n                  id: message.id,\r\n                  content: messageContent,\r\n                  conversationName: conversationInfo,\r\n                  sender: {\r\n                    id: message.sender.id,\r\n                    fullName: message.sender.email\r\n                      ? message.sender.email.split(\"@\")[0]\r\n                      : \"Người dùng\",\r\n                    profilePictureUrl: \"/images/default-avatar.png\",\r\n                  },\r\n                  date:\r\n                    typeof message.createdAt === \"string\"\r\n                      ? message.createdAt\r\n                      : new Date().toISOString(),\r\n                  highlighted: true,\r\n                  _searchContext: message._searchContext,\r\n                };\r\n              } catch (mapError) {\r\n                console.error(\"Error mapping message:\", mapError, message);\r\n                // Trả về một tin nhắn mặc định nếu có lỗi khi chuyển đổi\r\n                return {\r\n                  id: message.id || \"unknown-id\",\r\n                  content: \"Không thể hiển thị nội dung tin nhắn\",\r\n                  sender: {\r\n                    id: \"unknown\",\r\n                    fullName: \"Người dùng\",\r\n                    profilePictureUrl: \"/images/default-avatar.png\",\r\n                  },\r\n                  date: new Date().toLocaleDateString(),\r\n                  highlighted: true,\r\n                };\r\n              }\r\n            },\r\n          );\r\n\r\n          // Lọc bỏ các tin nhắn không hợp lệ\r\n          const validMessages = messages.filter(\r\n            (msg) => msg.id !== \"unknown-id\",\r\n          );\r\n          setFilteredMessages(validMessages);\r\n        } else {\r\n          console.log(\"No messages found or API returned error\");\r\n          // Hiển thị thông báo không tìm thấy kết quả\r\n          setFilteredMessages([]);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error searching messages:\", error);\r\n        setFilteredMessages([]);\r\n      }\r\n    };\r\n\r\n    // Gọi hàm tìm kiếm tin nhắn\r\n    searchForMessages();\r\n\r\n    if (isSearchingUser) {\r\n      // Nếu là số điện thoại hoặc email hợp lệ, gọi API tìm kiếm\r\n      const searchUserByValue = async () => {\r\n        // Kiểm tra xem người dùng đã đăng nhập hay chưa\r\n        if (!accessToken || !currentUser) {\r\n          setPhoneSearchResult(null);\r\n          return;\r\n        }\r\n\r\n        try {\r\n          // Gọi API tìm kiếm người dùng bằng số điện thoại hoặc email\r\n          const result = await searchUser(debouncedSearchQuery);\r\n\r\n          if (result.success && result.user) {\r\n            // API trả về dữ liệu người dùng trực tiếp, không phải trong trường user\r\n            const userData = result.user;\r\n            setPhoneSearchResult({\r\n              id: userData.id,\r\n              fullName: userData.userInfo?.fullName || \"Người dùng\",\r\n              profilePictureUrl:\r\n                userData.userInfo?.profilePictureUrl ||\r\n                \"/images/default-avatar.png\",\r\n              phoneNumber: userData.phoneNumber || debouncedSearchQuery, // Nếu tìm bằng email, có thể không có phoneNumber\r\n            });\r\n          } else {\r\n            setPhoneSearchResult(null);\r\n          }\r\n        } catch (error) {\r\n          // Xử lý lỗi 404 (không tìm thấy) và các lỗi khác\r\n          console.log(\"Error searching user:\", error);\r\n          // Không hiển thị lỗi, chỉ đặt kết quả tìm kiếm là null\r\n          setPhoneSearchResult(null);\r\n          // Không hiển thị toast lỗi, để UI hiển thị \"Không tìm thấy người dùng\"\r\n        }\r\n      };\r\n\r\n      // Gọi hàm tìm kiếm\r\n      searchUserByValue();\r\n    } else {\r\n      // Tìm kiếm bạn bè dựa trên tên\r\n      // Lọc danh sách bạn bè từ API\r\n      const filtered = allFriends.filter((friend) =>\r\n        friend.fullName\r\n          .toLowerCase()\r\n          .includes(debouncedSearchQuery.toLowerCase()),\r\n      );\r\n      setFilteredFriends(filtered);\r\n      setPhoneSearchResult(null);\r\n    }\r\n  }, [\r\n    debouncedSearchQuery,\r\n    allFriends,\r\n    isAddFriendMode,\r\n    accessToken,\r\n    currentUser,\r\n  ]);\r\n\r\n  // Handle search input change\r\n  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    setSearchQuery(e.target.value);\r\n    if (e.target.value) {\r\n      setShowResults(true);\r\n      setShowSuggestions(false);\r\n    } else {\r\n      setShowResults(false);\r\n      setShowSuggestions(true);\r\n    }\r\n  };\r\n\r\n  // Handle search activation\r\n  const activateSearch = () => {\r\n    setIsSearchActive(true);\r\n    setShowSuggestions(true);\r\n    // Dispatch a custom event to notify other components\r\n    const event = new CustomEvent(\"searchActivated\", {\r\n      detail: { active: true },\r\n    });\r\n    document.dispatchEvent(event);\r\n  };\r\n\r\n  // Handle search deactivation\r\n  const deactivateSearch = () => {\r\n    console.log(\"Deactivating search\");\r\n    setIsSearchActive(false);\r\n    setShowResults(false);\r\n    setShowSuggestions(false);\r\n    setSearchQuery(\"\");\r\n    setIsAddFriendMode(false);\r\n    // Dispatch a custom event to notify other components\r\n    const event = new CustomEvent(\"searchActivated\", {\r\n      detail: { active: false },\r\n    });\r\n    document.dispatchEvent(event);\r\n  };\r\n\r\n  // Handle add friend mode\r\n  const activateAddFriendMode = () => {\r\n    // Thay vì focus vào input, hiển thị dialog QR code\r\n    setShowQRCodeDialog(true);\r\n  };\r\n\r\n  // Handle user profile click\r\n  const handleUserClick = async (user: UserSearchResult) => {\r\n    try {\r\n      // Fetch complete user data using getUserDataById\r\n      const result = await getUserDataById(user.id);\r\n\r\n      if (result.success && result.user) {\r\n        // Use the complete user data from the API\r\n        setSelectedUser(result.user);\r\n      } else {\r\n        // Fallback to simplified user object if API call fails\r\n        console.error(\"Failed to fetch complete user data:\", result.error);\r\n        // Chuyển đổi từ UserSearchResult sang User\r\n        // Sử dụng type assertion để tránh lỗi TypeScript\r\n        const userForProfile = {\r\n          id: user.id,\r\n          userInfo: {\r\n            fullName: user.fullName,\r\n            profilePictureUrl: user.profilePictureUrl,\r\n          },\r\n          email: user.email,\r\n          phoneNumber: user.phoneNumber,\r\n        } as unknown as User;\r\n\r\n        setSelectedUser(userForProfile);\r\n      }\r\n\r\n      // Show the profile dialog\r\n      setShowProfileDialog(true);\r\n    } catch (error) {\r\n      console.error(\"Error fetching user data:\", error);\r\n      toast.error(\"Không thể tải thông tin người dùng\");\r\n    }\r\n  };\r\n\r\n  // Handle search submission\r\n  const handleSearchSubmit = () => {\r\n    if (searchQuery.trim() && !recentSearches.includes(searchQuery)) {\r\n      setRecentSearches((prev) => [searchQuery, ...prev.slice(0, 4)]);\r\n    }\r\n  };\r\n\r\n  // Handle selecting a recent search\r\n  const handleSelectRecentSearch = (search: string) => {\r\n    setSearchQuery(search);\r\n    setShowSuggestions(false);\r\n    setShowResults(true);\r\n  };\r\n\r\n  // Clear search\r\n  const clearSearch = () => {\r\n    setSearchQuery(\"\");\r\n  };\r\n\r\n  // Group friends by first letter for alphabetical display\r\n  const groupFriendsByLetter = () => {\r\n    const groups: { [key: string]: Friend[] } = {};\r\n\r\n    const friendsToGroup = searchQuery ? filteredFriends : allFriends;\r\n\r\n    friendsToGroup.forEach((friend) => {\r\n      const firstLetter = friend.fullName.charAt(0).toUpperCase();\r\n      if (!groups[firstLetter]) {\r\n        groups[firstLetter] = [];\r\n      }\r\n      groups[firstLetter].push(friend);\r\n    });\r\n\r\n    return groups;\r\n  };\r\n\r\n  const friendsByLetter = groupFriendsByLetter();\r\n\r\n  return (\r\n    <div\r\n      className={cn(`w-[300px] p-4 relative border-r bg-white`, className)}\r\n      ref={searchRef}\r\n    >\r\n      {/* Header with search input and buttons */}\r\n      <div className=\"flex items-center justify-between w-full\">\r\n        {!isSearchActive ? (\r\n          // Normal state - Search input with buttons\r\n          <>\r\n            <div className=\"relative w-[200px]\">\r\n              <div\r\n                className=\"flex items-center border border-gray-200 rounded-md px-2 h-8 w-full cursor-pointer\"\r\n                onClick={activateSearch}\r\n              >\r\n                <Search className=\"h-4 w-4 text-gray-500\" />\r\n                <div className=\"border-0 h-8 bg-transparent outline-none w-full text-xs ml-2 py-0 text-gray-400 flex items-center\">\r\n                  Tìm kiếm\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"flex items-center space-x-2\">\r\n              <button\r\n                className=\"h-8 w-8 rounded-full hover:bg-gray-100 flex items-center justify-center\"\r\n                title=\"Tìm kiếm và kết bạn\"\r\n                onClick={activateAddFriendMode}\r\n              >\r\n                <UserPlus className=\"h-5 w-5\" />\r\n              </button>\r\n              <button\r\n                className=\"h-8 w-8 rounded-full hover:bg-gray-100 flex items-center justify-center\"\r\n                title=\"Tạo nhóm\"\r\n                onClick={() => setShowCreateGroupDialog(true)}\r\n              >\r\n                <Users className=\"h-5 w-5\" />\r\n              </button>\r\n            </div>\r\n          </>\r\n        ) : (\r\n          // Active search state - Full width input with close button\r\n          <>\r\n            <div className=\"relative flex-1 mr-2\">\r\n              <div className=\"flex items-center border border-gray-200 rounded-md px-2 h-8 w-full bg-gray-50\">\r\n                <Search className=\"h-4 w-4 text-gray-500\" />\r\n                <input\r\n                  placeholder=\"Tìm kiếm\"\r\n                  className=\"border-0 h-8 bg-transparent outline-none w-full text-xs placeholder:text-[0.8125rem] ml-2 py-0\"\r\n                  value={searchQuery}\r\n                  onChange={handleSearchChange}\r\n                  autoFocus\r\n                  onKeyDown={(e) => {\r\n                    if (e.key === \"Enter\") {\r\n                      handleSearchSubmit();\r\n                    }\r\n                  }}\r\n                />\r\n                {searchQuery && (\r\n                  <button onClick={clearSearch} className=\"flex-shrink-0\">\r\n                    <X className=\"h-4 w-4 text-gray-500\" />\r\n                  </button>\r\n                )}\r\n              </div>\r\n            </div>\r\n\r\n            <button\r\n              onClick={deactivateSearch}\r\n              className=\"h-8 px-3 rounded-md bg-gray-100 hover:bg-gray-200 flex items-center justify-center text-sm font-medium\"\r\n              title=\"Đóng\"\r\n            >\r\n              Đóng\r\n            </button>\r\n          </>\r\n        )}\r\n      </div>\r\n\r\n      {/* Search Suggestions Dropdown - Positioned absolutely relative to the parent */}\r\n      {isSearchActive && showSuggestions && (\r\n        <div className=\"absolute left-0 top-[60px] w-full bg-white border-t border-gray-200 shadow-lg z-50\">\r\n          <div className=\"p-3 border-b border-gray-100\">\r\n            <div className=\"text-sm font-medium mb-2\">Tìm gần đây</div>\r\n            {recentSearches.length > 0 ? (\r\n              <div className=\"space-y-2\">\r\n                {recentSearches.map((search, index) => (\r\n                  <div\r\n                    key={index}\r\n                    className=\"flex items-center justify-between hover:bg-gray-50 p-2 rounded-md cursor-pointer\"\r\n                    onClick={() => handleSelectRecentSearch(search)}\r\n                  >\r\n                    <div className=\"flex items-center\">\r\n                      <Clock className=\"h-4 w-4 text-gray-400 mr-2\" />\r\n                      <span className=\"text-sm\">{search}</span>\r\n                    </div>\r\n                    <button\r\n                      className=\"h-6 w-6 rounded-full hover:bg-gray-200 flex items-center justify-center\"\r\n                      onClick={(e) => {\r\n                        e.stopPropagation();\r\n                        setRecentSearches((prev) =>\r\n                          prev.filter((_, i) => i !== index),\r\n                        );\r\n                      }}\r\n                    >\r\n                      <X className=\"h-3 w-3 text-gray-400\" />\r\n                    </button>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            ) : (\r\n              <div className=\"text-sm text-gray-500 py-2\">\r\n                Không có tìm kiếm nào gần đây\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          <div className=\"p-3\">\r\n            <div className=\"text-sm font-medium mb-2\">Lọc tin nhắn</div>\r\n            <div className=\"flex space-x-2\">\r\n              <div className=\"bg-gray-100 rounded-full px-3 py-1 flex items-center cursor-pointer hover:bg-gray-200\">\r\n                <Bell className=\"h-4 w-4 text-gray-600 mr-1\" />\r\n                <span className=\"text-sm\">Nhắc bạn</span>\r\n              </div>\r\n              <div className=\"bg-gray-100 rounded-full px-3 py-1 flex items-center cursor-pointer hover:bg-gray-200\">\r\n                <SmilePlus className=\"h-4 w-4 text-gray-600 mr-1\" />\r\n                <span className=\"text-sm\">Biểu cảm</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Profile Dialog */}\r\n      {showProfileDialog && selectedUser && (\r\n        <ProfileDialog\r\n          isOpen={showProfileDialog}\r\n          onOpenChange={(open) => setShowProfileDialog(open)}\r\n          user={selectedUser}\r\n          isOwnProfile={selectedUser?.id === currentUser?.id}\r\n          onChat={() => {\r\n            // Xử lý khi nhấn nút nhắn tin\r\n            setShowProfileDialog(false);\r\n          }}\r\n          onCall={() => {\r\n            // Xử lý khi nhấn nút gọi điện\r\n            setShowProfileDialog(false);\r\n          }}\r\n        />\r\n      )}\r\n\r\n      {/* QR Code Dialog */}\r\n      {currentUser && (\r\n        <QRCodeDialog\r\n          isOpen={showQRCodeDialog}\r\n          onClose={() => setShowQRCodeDialog(false)}\r\n          userId={currentUser.id}\r\n        />\r\n      )}\r\n\r\n      {/* Create Group Dialog */}\r\n      <CreateGroupDialog\r\n        isOpen={showCreateGroupDialog}\r\n        onOpenChange={setShowCreateGroupDialog}\r\n      />\r\n\r\n      {/* Search Results Dropdown */}\r\n      {isSearchActive && showResults && searchQuery && (\r\n        <div className=\"absolute left-0 top-[60px] w-full bg-white border-t border-gray-200 shadow-lg z-50 overflow-hidden\">\r\n          {/* Tab navigation */}\r\n          <div className=\"flex border-b border-gray-200 bg-white\">\r\n            <button className=\"px-4 py-2 text-[13px] font-semibold text-blue-600 border-b-2 border-blue-600\">\r\n              Tất cả\r\n            </button>\r\n            <button className=\"px-4 py-2 text-[13px] font-semibold text-gray-500 hover:text-gray-700\">\r\n              Liên hệ\r\n            </button>\r\n            <button className=\"px-4 py-2 text-[13px] font-semibold text-gray-500 hover:text-gray-700\">\r\n              Tin nhắn\r\n            </button>\r\n            <button className=\"px-4 py-2 text-[13px] font-semibold text-gray-500 hover:text-gray-700\">\r\n              File\r\n            </button>\r\n          </div>\r\n\r\n          {/* User search section */}\r\n          {isSearchingUser && (\r\n            <div className=\"border-b border-gray-100\">\r\n              <div className=\"p-3\">\r\n                <div className=\"text-sm font-medium mb-2\">\r\n                  Tìm bạn qua{\" \"}\r\n                  {isEmail(debouncedSearchQuery) ? \"email\" : \"số điện thoại\"}:\r\n                </div>\r\n\r\n                {phoneSearchResult ? (\r\n                  <div\r\n                    className=\"flex items-center py-2 px-1 hover:bg-gray-50 cursor-pointer rounded-md\"\r\n                    onClick={() => handleUserClick(phoneSearchResult)}\r\n                  >\r\n                    <div className=\"flex items-center w-full\">\r\n                      <div className=\"h-10 w-10 rounded-full overflow-hidden mr-3\">\r\n                        <Image\r\n                          src={phoneSearchResult.profilePictureUrl}\r\n                          alt={phoneSearchResult.fullName}\r\n                          width={40}\r\n                          height={40}\r\n                          className=\"object-cover\"\r\n                        />\r\n                      </div>\r\n                      <div>\r\n                        <div className=\"text-sm font-medium\">\r\n                          {phoneSearchResult.fullName}\r\n                        </div>\r\n                        <div className=\"text-xs text-gray-500\">\r\n                          {isEmail(debouncedSearchQuery)\r\n                            ? \"Email\"\r\n                            : \"Số điện thoại\"}\r\n                          :{\" \"}\r\n                          <span className=\"text-blue-500\">\r\n                            {isEmail(debouncedSearchQuery)\r\n                              ? phoneSearchResult.email || debouncedSearchQuery\r\n                              : phoneSearchResult.phoneNumber}\r\n                          </span>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                ) : (\r\n                  <div className=\"text-sm text-gray-500 py-2\">\r\n                    Không tìm thấy người dùng nào với{\" \"}\r\n                    {isEmail(debouncedSearchQuery) ? \"email\" : \"số điện thoại\"}{\" \"}\r\n                    này\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {filteredMessages.length > 0 && (\r\n            <div className=\"border-b border-gray-100\">\r\n              <div className=\"text-sm font-medium p-3\">\r\n                Tin nhắn{\" \"}\r\n                {filteredMessages.length > 20\r\n                  ? \"(20+)\"\r\n                  : `(${filteredMessages.length})`}\r\n              </div>\r\n\r\n              {filteredMessages.map((message) => {\r\n                // Nếu là tin nhắn đang tải, hiển thị trạng thái đang tải\r\n                if (message.id === \"loading\") {\r\n                  return (\r\n                    <div\r\n                      key=\"loading\"\r\n                      className=\"flex items-center py-2 px-1 rounded-md\"\r\n                    >\r\n                      <div className=\"flex-1 text-center\">\r\n                        <div className=\"animate-pulse flex space-x-4 items-center\">\r\n                          <div className=\"rounded-full bg-gray-200 h-10 w-10\"></div>\r\n                          <div className=\"flex-1 space-y-2 py-1\">\r\n                            <div className=\"h-2 bg-gray-200 rounded w-3/4\"></div>\r\n                            <div className=\"h-2 bg-gray-200 rounded w-1/2\"></div>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  );\r\n                }\r\n\r\n                // Xử lý hiển thị tin nhắn bình thường\r\n                const content = message.content;\r\n                const lowerContent = content.toLowerCase();\r\n                const lowerQuery = debouncedSearchQuery.toLowerCase();\r\n                const startIndex = lowerContent.indexOf(lowerQuery);\r\n\r\n                // Tách nội dung để đánh dấu từ khóa\r\n                const beforeText =\r\n                  startIndex >= 0 ? content.substring(0, startIndex) : \"\";\r\n                const highlightedText =\r\n                  startIndex >= 0\r\n                    ? content.substring(\r\n                        startIndex,\r\n                        startIndex + debouncedSearchQuery.length,\r\n                      )\r\n                    : \"\";\r\n                const afterText =\r\n                  startIndex >= 0\r\n                    ? content.substring(\r\n                        startIndex + debouncedSearchQuery.length,\r\n                      )\r\n                    : content;\r\n\r\n                // Tính thời gian hiển thị\r\n                const messageDate = new Date(message.date);\r\n                const now = new Date();\r\n                const diffInMs = now.getTime() - messageDate.getTime();\r\n                const diffInMinutes = Math.floor(diffInMs / (1000 * 60));\r\n                const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));\r\n\r\n                let timeDisplay = message.date;\r\n                if (diffInMinutes < 60) {\r\n                  timeDisplay = `${diffInMinutes} phút`;\r\n                } else if (diffInHours < 24) {\r\n                  timeDisplay = `${diffInHours} giờ`;\r\n                } else if (diffInHours < 48) {\r\n                  timeDisplay = \"1 ngày\";\r\n                } else {\r\n                  const diffInDays = Math.floor(diffInHours / 24);\r\n                  timeDisplay = `${diffInDays} ngày`;\r\n                }\r\n\r\n                return (\r\n                  <div\r\n                    key={message.id}\r\n                    className=\"flex py-3 hover:bg-gray-50 cursor-pointer px-3 border-b border-gray-100\"\r\n                    onClick={async (e) => {\r\n                      e.preventDefault();\r\n                      e.stopPropagation();\r\n\r\n                      // Lưu ID người dùng trước khi đóng giao diện tìm kiếm\r\n                      let userIdToOpen: string | null = null;\r\n\r\n                      // Get the correct user ID to open the chat\r\n                      // If the search context has a userId, use that (this is the conversation partner ID)\r\n                      const contextUserId = message._searchContext?.userId;\r\n\r\n                      if (contextUserId) {\r\n                        userIdToOpen = contextUserId;\r\n                      } else {\r\n                        // Fallback: If no context userId, check if sender is not the current user\r\n                        const senderId = message.sender.id;\r\n                        if (senderId && senderId !== currentUser?.id) {\r\n                          userIdToOpen = senderId;\r\n                        }\r\n                      }\r\n\r\n                      if (userIdToOpen) {\r\n                        try {\r\n                          // Lưu ID vào biến tạm thời\r\n                          const idToOpen = userIdToOpen;\r\n\r\n                          // Gọi openChat trước khi đóng giao diện tìm kiếm\r\n                          // Điều này đảm bảo rằng chúng ta đã bắt đầu quá trình mở chat\r\n                          // trước khi component có thể bị unmount\r\n                          const success = await openChat(idToOpen, \"USER\");\r\n\r\n                          // Sau đó mới đóng giao diện tìm kiếm\r\n                          deactivateSearch();\r\n\r\n                          if (success) {\r\n                            // Chuyển hướng đến trang chat\r\n                            toast.success(\"Mở cuộc trò chuyện thành công\");\r\n                            router.push(\"/dashboard/chat\");\r\n                          } else {\r\n                            toast.error(\"Không thể mở cuộc trò chuyện này\");\r\n                          }\r\n                        } catch (error) {\r\n                          console.error(\"Error opening chat:\", error);\r\n                          toast.error(\"Có lỗi xảy ra khi mở cuộc trò chuyện\");\r\n                          // Vẫn đóng giao diện tìm kiếm nếu có lỗi\r\n                          deactivateSearch();\r\n                        }\r\n                      } else {\r\n                        toast.error(\"Không thể mở cuộc trò chuyện này\");\r\n                      }\r\n                    }}\r\n                  >\r\n                    <div className=\"h-10 w-10 rounded-full overflow-hidden mr-3 flex-shrink-0\">\r\n                      {senderDetails[message.sender.id]?.userInfo\r\n                        ?.profilePictureUrl &&\r\n                      typeof senderDetails[message.sender.id]?.userInfo\r\n                        ?.profilePictureUrl === \"string\" &&\r\n                      senderDetails[\r\n                        message.sender.id\r\n                      ]?.userInfo?.profilePictureUrl?.trim() !== \"\" ? (\r\n                        <Image\r\n                          src={\r\n                            senderDetails[message.sender.id]?.userInfo\r\n                              ?.profilePictureUrl ||\r\n                            \"/images/default-avatar.png\"\r\n                          }\r\n                          alt={\r\n                            senderDetails[message.sender.id]?.userInfo\r\n                              ?.fullName || \"Người dùng\"\r\n                          }\r\n                          width={40}\r\n                          height={40}\r\n                          className=\"object-cover\"\r\n                        />\r\n                      ) : (\r\n                        <div className=\"h-10 w-10 rounded-full bg-blue-500 flex items-center justify-center text-white\">\r\n                          <span>\r\n                            {(\r\n                              senderDetails[message.sender.id]?.userInfo\r\n                                ?.fullName || \"Người dùng\"\r\n                            ).charAt(0)}\r\n                          </span>\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                    <div className=\"flex-1 overflow-hidden\">\r\n                      <div className=\"flex flex-col\">\r\n                        <div className=\"flex justify-between items-start\">\r\n                          <div className=\"text-sm font-medium\">\r\n                            {senderDetails[message.sender.id]?.userInfo\r\n                              ?.fullName || \"Người dùng\"}\r\n                          </div>\r\n                          <div className=\"text-xs text-gray-500 ml-2 flex-shrink-0\">\r\n                            {timeDisplay}\r\n                          </div>\r\n                        </div>\r\n                        <div className=\"text-sm text-gray-700 truncate\">\r\n                          {startIndex >= 0 ? (\r\n                            <span>\r\n                              <span className=\"font-medium\">\r\n                                {senderDetails[message.sender.id]?.userInfo\r\n                                  ?.fullName || \"Người dùng\"}\r\n                                :{\" \"}\r\n                              </span>\r\n                              {beforeText}\r\n                              <span className=\"text-blue-500 font-medium\">\r\n                                {highlightedText}\r\n                              </span>\r\n                              {afterText}\r\n                            </span>\r\n                          ) : (\r\n                            <span>\r\n                              <span className=\"font-medium\">\r\n                                {senderDetails[message.sender.id]?.userInfo\r\n                                  ?.fullName || \"Người dùng\"}\r\n                                :{\" \"}\r\n                              </span>\r\n                              {content}\r\n                            </span>\r\n                          )}\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                );\r\n              })}\r\n            </div>\r\n          )}\r\n\r\n          {isLoadingFriends ? (\r\n            <div className=\"p-4 text-center\">\r\n              <div className=\"animate-spin h-6 w-6 border-2 border-blue-500 rounded-full border-t-transparent mx-auto mb-2\"></div>\r\n              <p className=\"text-sm text-gray-500\">\r\n                Đang tải danh sách bạn bè...\r\n              </p>\r\n            </div>\r\n          ) : (\r\n            filteredFriends.length > 0 && (\r\n              <div className=\"p-2 border-b border-gray-100\">\r\n                <div className=\"flex justify-between items-center\">\r\n                  <div className=\"text-sm font-medium\">\r\n                    Bạn bè ({filteredFriends.length})\r\n                  </div>\r\n                  <div className=\"flex items-center\">\r\n                    <button className=\"text-xs text-blue-500 hover:underline mr-1\">\r\n                      Tất cả\r\n                    </button>\r\n                    <MoreHorizontal className=\"h-4 w-4 text-gray-400\" />\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            )\r\n          )}\r\n\r\n          {filteredFriends.length > 0 && (\r\n            <div className=\"max-h-[400px] overflow-y-auto no-scrollbar\">\r\n              <div className=\"px-3 py-2 border-b border-gray-100\">\r\n                <div className=\"text-xs text-gray-500\">Tên (A-Z)</div>\r\n              </div>\r\n\r\n              {/* Alphabetical sections */}\r\n              {Object.keys(friendsByLetter)\r\n                .sort()\r\n                .map((letter) => (\r\n                  <div key={letter}>\r\n                    {/* Letter header */}\r\n                    <div className=\"px-3 py-1 bg-gray-50 text-xs font-medium text-gray-500\">\r\n                      {letter}\r\n                    </div>\r\n\r\n                    {/* Friends list */}\r\n                    {friendsByLetter[letter].map((friend) => (\r\n                      <div\r\n                        key={friend.id}\r\n                        className=\"flex items-center justify-between py-2 hover:bg-gray-50 cursor-pointer px-3\"\r\n                        onClick={() =>\r\n                          handleUserClick({\r\n                            id: friend.id,\r\n                            fullName: friend.fullName,\r\n                            profilePictureUrl: friend.profilePictureUrl,\r\n                            phoneNumber: friend.phoneNumber || \"\", // Sử dụng số điện thoại từ friend nếu có\r\n                            email: friend.email || \"\", // Sử dụng email từ friend nếu có\r\n                          })\r\n                        }\r\n                      >\r\n                        <div className=\"flex items-center\">\r\n                          <div className=\"h-8 w-8 rounded-full overflow-hidden mr-2\">\r\n                            <Image\r\n                              src={\r\n                                friend.profilePictureUrl &&\r\n                                typeof friend.profilePictureUrl === \"string\" &&\r\n                                friend.profilePictureUrl?.trim() !== \"\"\r\n                                  ? friend.profilePictureUrl\r\n                                  : \"/images/default-avatar.png\"\r\n                              }\r\n                              alt={friend.fullName}\r\n                              width={32}\r\n                              height={32}\r\n                              className=\"object-cover\"\r\n                            />\r\n                          </div>\r\n                          <span className=\"text-sm\">{friend.fullName}</span>\r\n                        </div>\r\n                        <button\r\n                          className=\"h-6 w-6 rounded-full hover:bg-gray-200 flex items-center justify-center\"\r\n                          onClick={(e) => {\r\n                            e.stopPropagation();\r\n                            // Thêm xử lý menu nếu cần\r\n                          }}\r\n                        >\r\n                          <MoreHorizontal className=\"h-4 w-4 text-gray-400\" />\r\n                        </button>\r\n                      </div>\r\n                    ))}\r\n                  </div>\r\n                ))}\r\n            </div>\r\n          )}\r\n\r\n          {/* No results message - only show when no messages and no friends match */}\r\n          {searchQuery &&\r\n            filteredMessages.length === 0 &&\r\n            filteredFriends.length === 0 && (\r\n              <div className=\"p-4 text-center\">\r\n                <div className=\"flex justify-center mb-4\">\r\n                  <div className=\"relative w-24 h-24\">\r\n                    <div className=\"absolute inset-0 flex items-center justify-center\">\r\n                      <div className=\"w-16 h-16 rounded-full border-2 border-blue-200 flex items-center justify-center\">\r\n                        <Search className=\"h-8 w-8 text-blue-300\" />\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <p className=\"text-gray-500 text-sm\">Không tìm thấy kết quả</p>\r\n                <p className=\"text-gray-500 text-xs mt-1\">\r\n                  Vui lòng thử lại với từ khóa khác.\r\n                </p>\r\n              </div>\r\n            )}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AA3BA;;;;;;;;;;;;;;;;;;AAuEe,SAAS,aAAa,EAAE,SAAS,EAA0B;IACxE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IACxD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAChE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAC9D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACzD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAErD,EAAE;IACJ,mDAAmD;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAC/C,CAAC;IAGH,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjE,MAAM,CAAC,mBAAmB,qBAAqB,GAC7C,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B;IACpC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAChE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAClE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IACpE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAChE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAClE,MAAM,CAAC,uBAAuB,yBAAyB,GACrD,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IACpB,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IACzC,MAAM,uBAAuB,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD,EAAE,aAAa;IACtD,MAAM,EAAE,WAAW,EAAE,MAAM,WAAW,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD;IACtD,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,sEAAsE;IACtE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,gDAAgD;YAChD,IAAI,CAAC,eAAe,CAAC,aAAa;gBAChC,cAAc,EAAE;gBAChB;YACF;YAEA,IAAI;gBACF,oBAAoB;gBACpB,MAAM,SAAS,MAAM,CAAA,GAAA,sJAAA,CAAA,iBAAc,AAAD,EAAE;gBACpC,IAAI,OAAO,OAAO,IAAI,OAAO,OAAO,EAAE;oBACpC,cAAc,OAAO,OAAO;gBAC9B,OAAO;oBACL,QAAQ,KAAK,CAAC,4BAA4B,OAAO,KAAK;gBACxD;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2BAA2B;YAC3C,SAAU;gBACR,oBAAoB;YACtB;QACF;QAEA;IACF,GAAG;QAAC;QAAa;KAAY;IAE7B,mEAAmE;IACnE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,0DAA0D;QAC1D,IAAI,iBAAiB,MAAM,KAAK,KAAK,CAAC,eAAe,CAAC,aAAa;YACjE;QACF;QAEA,8CAA8C;QAC9C,0CAA0C;QAC1C,MAAM,YAAY,iBACf,MAAM,CAAC,CAAC;YACP,+CAA+C;YAC/C,OACE,IAAI,MAAM,IACV,IAAI,MAAM,CAAC,EAAE,IACb,IAAI,MAAM,CAAC,EAAE,CAAC,IAAI,OAAO,MACzB,IAAI,MAAM,CAAC,EAAE,KAAK,YAClB,IAAI,MAAM,CAAC,EAAE,KAAK,aAClB,IAAI,MAAM,CAAC,EAAE,KAAK;QAEtB,GACC,GAAG,CAAC,CAAC,MAAQ,IAAI,MAAM,CAAC,EAAE,CAC3B,uBAAuB;SACtB,MAAM,CAAC,CAAC,IAAI,OAAO,OAAS,KAAK,OAAO,CAAC,QAAQ;QAEpD,2CAA2C;QAC3C,IAAI,UAAU,MAAM,KAAK,GAAG;YAC1B;QACF;QAEA,iCAAiC;QACjC,MAAM,qBAAqB;YACzB,KAAK,MAAM,YAAY,UAAW;gBAChC,0DAA0D;gBAC1D,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE;oBAC5B,IAAI;wBACF,kCAAkC;wBAClC,MAAM,SAAS,MAAM,CAAA,GAAA,gIAAA,CAAA,kBAAe,AAAD,EAAE;wBAErC,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;4BACjC,yCAAyC;4BACzC,iBAAiB,CAAC,OAAS,CAAC;oCAC1B,GAAG,IAAI;oCACP,CAAC,SAAS,EAAE,OAAO,IAAI;gCACzB,CAAC;wBACH;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,CAAC,6BAA6B,EAAE,SAAS,CAAC,CAAC,EAAE;oBAC3D,gDAAgD;oBAClD;gBACF;YACF;QACF;QAEA;IACF,GAAG;QAAC;QAAkB;QAAa;QAAa;KAAc;IAE9D,+DAA+D;IAC/D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,mBAAmB,KAAiB;YAC3C,IACE,UAAU,OAAO,IACjB,CAAC,UAAU,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GACxC;gBACA,eAAe;gBACf,mBAAmB;gBACnB,kBAAkB;YACpB;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG,EAAE;IAEL,kBAAkB;IAClB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,sBAAsB;YACzB,mBAAmB,EAAE;YACrB,oBAAoB,EAAE;YACtB,qBAAqB;YACrB,mBAAmB;YACnB;QACF;QAEA,sDAAsD;QACtD,MAAM,UAAU,CAAA,GAAA,uHAAA,CAAA,gBAAa,AAAD,EAAE;QAC9B,MAAM,eAAe,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD,EAAE;QAC7B,MAAM,kBAAkB,WAAW;QACnC,mBAAmB;QAEnB,qCAAqC;QACrC,MAAM,oBAAoB;YACxB,gDAAgD;YAChD,IAAI,CAAC,eAAe,CAAC,aAAa;gBAChC,oBAAoB,EAAE;gBACtB;YACF;YAEA,IAAI;gBACF,QAAQ,GAAG,CAAC,kCAAkC;gBAC9C,oCAAoC;gBACpC,oBAAoB;oBAClB;wBACE,IAAI;wBACJ,SAAS;wBACT,QAAQ;4BACN,IAAI;4BACJ,UAAU;4BACV,mBAAmB;wBACrB;wBACA,MAAM,IAAI,OAAO,kBAAkB;wBACnC,aAAa;oBACf;iBACD;gBAED,0DAA0D;gBAC1D,MAAM,YAAY,WAAW,GAAG,CAAC,CAAC,SAAW,OAAO,EAAE;gBAEtD,8CAA8C;gBAC9C,IAAI,UAAU,MAAM,KAAK,GAAG;oBAC1B,oBAAoB,EAAE;oBACtB;gBACF;gBAEA,mEAAmE;gBACnE,MAAM,SAAS,MAAM,CAAA,GAAA,mIAAA,CAAA,uBAAoB,AAAD,EACtC,sBACA;gBAGF,IAAI,OAAO,OAAO,IAAI,OAAO,QAAQ,IAAI,OAAO,QAAQ,CAAC,MAAM,GAAG,GAAG;oBACnE,QAAQ,GAAG,CAAC,mBAAmB,OAAO,QAAQ,CAAC,MAAM;oBACrD,wEAAwE;oBACxE,MAAM,WAAkC,OAAO,QAAQ,CAAC,GAAG,CACzD,CAAC;wBACC,IAAI;4BACF,0BAA0B;4BAC1B,IAAI,iBAAiB;4BACrB,IAAI,OAAO,QAAQ,OAAO,KAAK,UAAU;gCACvC,iBAAiB,QAAQ,OAAO;4BAClC,OAAO,IACL,QAAQ,OAAO,IACf,OAAO,QAAQ,OAAO,KAAK,UAC3B;gCACA,uDAAuD;gCACvD,iBAAiB,QAAQ,OAAO,CAAC,IAAI,IAAI;4BAC3C;4BAEA,uDAAuD;4BAEvD,2DAA2D;4BAC3D,kEAAkE;4BAElE,mCAAmC;4BACnC,IAAI,mBAAmB;4BACvB,IAAI,QAAQ,cAAc,IAAI,QAAQ,cAAc,CAAC,MAAM,EAAE;gCAC3D,yCAAyC;gCACzC,MAAM,SAAS,WAAW,IAAI,CAC5B,CAAC,IAAM,EAAE,EAAE,KAAK,QAAQ,cAAc,EAAE;gCAE1C,IAAI,QAAQ;oCACV,mBAAmB,OAAO,QAAQ;gCACpC;4BACF;4BAEA,0CAA0C;4BAC1C,OAAO;gCACL,IAAI,QAAQ,EAAE;gCACd,SAAS;gCACT,kBAAkB;gCAClB,QAAQ;oCACN,IAAI,QAAQ,MAAM,CAAC,EAAE;oCACrB,UAAU,QAAQ,MAAM,CAAC,KAAK,GAC1B,QAAQ,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,GAClC;oCACJ,mBAAmB;gCACrB;gCACA,MACE,OAAO,QAAQ,SAAS,KAAK,WACzB,QAAQ,SAAS,GACjB,IAAI,OAAO,WAAW;gCAC5B,aAAa;gCACb,gBAAgB,QAAQ,cAAc;4BACxC;wBACF,EAAE,OAAO,UAAU;4BACjB,QAAQ,KAAK,CAAC,0BAA0B,UAAU;4BAClD,yDAAyD;4BACzD,OAAO;gCACL,IAAI,QAAQ,EAAE,IAAI;gCAClB,SAAS;gCACT,QAAQ;oCACN,IAAI;oCACJ,UAAU;oCACV,mBAAmB;gCACrB;gCACA,MAAM,IAAI,OAAO,kBAAkB;gCACnC,aAAa;4BACf;wBACF;oBACF;oBAGF,mCAAmC;oBACnC,MAAM,gBAAgB,SAAS,MAAM,CACnC,CAAC,MAAQ,IAAI,EAAE,KAAK;oBAEtB,oBAAoB;gBACtB,OAAO;oBACL,QAAQ,GAAG,CAAC;oBACZ,4CAA4C;oBAC5C,oBAAoB,EAAE;gBACxB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C,oBAAoB,EAAE;YACxB;QACF;QAEA,4BAA4B;QAC5B;QAEA,IAAI,iBAAiB;YACnB,2DAA2D;YAC3D,MAAM,oBAAoB;gBACxB,gDAAgD;gBAChD,IAAI,CAAC,eAAe,CAAC,aAAa;oBAChC,qBAAqB;oBACrB;gBACF;gBAEA,IAAI;oBACF,4DAA4D;oBAC5D,MAAM,SAAS,MAAM,CAAA,GAAA,gIAAA,CAAA,aAAU,AAAD,EAAE;oBAEhC,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;wBACjC,wEAAwE;wBACxE,MAAM,WAAW,OAAO,IAAI;wBAC5B,qBAAqB;4BACnB,IAAI,SAAS,EAAE;4BACf,UAAU,SAAS,QAAQ,EAAE,YAAY;4BACzC,mBACE,SAAS,QAAQ,EAAE,qBACnB;4BACF,aAAa,SAAS,WAAW,IAAI;wBACvC;oBACF,OAAO;wBACL,qBAAqB;oBACvB;gBACF,EAAE,OAAO,OAAO;oBACd,iDAAiD;oBACjD,QAAQ,GAAG,CAAC,yBAAyB;oBACrC,uDAAuD;oBACvD,qBAAqB;gBACrB,uEAAuE;gBACzE;YACF;YAEA,mBAAmB;YACnB;QACF,OAAO;YACL,+BAA+B;YAC/B,8BAA8B;YAC9B,MAAM,WAAW,WAAW,MAAM,CAAC,CAAC,SAClC,OAAO,QAAQ,CACZ,WAAW,GACX,QAAQ,CAAC,qBAAqB,WAAW;YAE9C,mBAAmB;YACnB,qBAAqB;QACvB;IACF,GAAG;QACD;QACA;QACA;QACA;QACA;KACD;IAED,6BAA6B;IAC7B,MAAM,qBAAqB,CAAC;QAC1B,eAAe,EAAE,MAAM,CAAC,KAAK;QAC7B,IAAI,EAAE,MAAM,CAAC,KAAK,EAAE;YAClB,eAAe;YACf,mBAAmB;QACrB,OAAO;YACL,eAAe;YACf,mBAAmB;QACrB;IACF;IAEA,2BAA2B;IAC3B,MAAM,iBAAiB;QACrB,kBAAkB;QAClB,mBAAmB;QACnB,qDAAqD;QACrD,MAAM,QAAQ,IAAI,YAAY,mBAAmB;YAC/C,QAAQ;gBAAE,QAAQ;YAAK;QACzB;QACA,SAAS,aAAa,CAAC;IACzB;IAEA,6BAA6B;IAC7B,MAAM,mBAAmB;QACvB,QAAQ,GAAG,CAAC;QACZ,kBAAkB;QAClB,eAAe;QACf,mBAAmB;QACnB,eAAe;QACf,mBAAmB;QACnB,qDAAqD;QACrD,MAAM,QAAQ,IAAI,YAAY,mBAAmB;YAC/C,QAAQ;gBAAE,QAAQ;YAAM;QAC1B;QACA,SAAS,aAAa,CAAC;IACzB;IAEA,yBAAyB;IACzB,MAAM,wBAAwB;QAC5B,mDAAmD;QACnD,oBAAoB;IACtB;IAEA,4BAA4B;IAC5B,MAAM,kBAAkB,OAAO;QAC7B,IAAI;YACF,iDAAiD;YACjD,MAAM,SAAS,MAAM,CAAA,GAAA,gIAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,EAAE;YAE5C,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;gBACjC,0CAA0C;gBAC1C,gBAAgB,OAAO,IAAI;YAC7B,OAAO;gBACL,uDAAuD;gBACvD,QAAQ,KAAK,CAAC,uCAAuC,OAAO,KAAK;gBACjE,2CAA2C;gBAC3C,iDAAiD;gBACjD,MAAM,iBAAiB;oBACrB,IAAI,KAAK,EAAE;oBACX,UAAU;wBACR,UAAU,KAAK,QAAQ;wBACvB,mBAAmB,KAAK,iBAAiB;oBAC3C;oBACA,OAAO,KAAK,KAAK;oBACjB,aAAa,KAAK,WAAW;gBAC/B;gBAEA,gBAAgB;YAClB;YAEA,0BAA0B;YAC1B,qBAAqB;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,2BAA2B;IAC3B,MAAM,qBAAqB;QACzB,IAAI,YAAY,IAAI,MAAM,CAAC,eAAe,QAAQ,CAAC,cAAc;YAC/D,kBAAkB,CAAC,OAAS;oBAAC;uBAAgB,KAAK,KAAK,CAAC,GAAG;iBAAG;QAChE;IACF;IAEA,mCAAmC;IACnC,MAAM,2BAA2B,CAAC;QAChC,eAAe;QACf,mBAAmB;QACnB,eAAe;IACjB;IAEA,eAAe;IACf,MAAM,cAAc;QAClB,eAAe;IACjB;IAEA,yDAAyD;IACzD,MAAM,uBAAuB;QAC3B,MAAM,SAAsC,CAAC;QAE7C,MAAM,iBAAiB,cAAc,kBAAkB;QAEvD,eAAe,OAAO,CAAC,CAAC;YACtB,MAAM,cAAc,OAAO,QAAQ,CAAC,MAAM,CAAC,GAAG,WAAW;YACzD,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;gBACxB,MAAM,CAAC,YAAY,GAAG,EAAE;YAC1B;YACA,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC;QAC3B;QAEA,OAAO;IACT;IAEA,MAAM,kBAAkB;IAExB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,CAAC,wCAAwC,CAAC,EAAE;QAC1D,KAAK;;0BAGL,8OAAC;gBAAI,WAAU;0BACZ,CAAC,iBACA,2CAA2C;8BAC3C;;sCACE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,WAAU;gCACV,SAAS;;kDAET,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCAAI,WAAU;kDAAoG;;;;;;;;;;;;;;;;;sCAMvH,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,WAAU;oCACV,OAAM;oCACN,SAAS;8CAET,cAAA,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;8CAEtB,8OAAC;oCACC,WAAU;oCACV,OAAM;oCACN,SAAS,IAAM,yBAAyB;8CAExC,cAAA,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;;;;;;;mCAKvB,2DAA2D;8BAC3D;;sCACE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCACC,aAAY;wCACZ,WAAU;wCACV,OAAO;wCACP,UAAU;wCACV,SAAS;wCACT,WAAW,CAAC;4CACV,IAAI,EAAE,GAAG,KAAK,SAAS;gDACrB;4CACF;wCACF;;;;;;oCAED,6BACC,8OAAC;wCAAO,SAAS;wCAAa,WAAU;kDACtC,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAMrB,8OAAC;4BACC,SAAS;4BACT,WAAU;4BACV,OAAM;sCACP;;;;;;;;;;;;;YAQN,kBAAkB,iCACjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAA2B;;;;;;4BACzC,eAAe,MAAM,GAAG,kBACvB,8OAAC;gCAAI,WAAU;0CACZ,eAAe,GAAG,CAAC,CAAC,QAAQ,sBAC3B,8OAAC;wCAEC,WAAU;wCACV,SAAS,IAAM,yBAAyB;;0DAExC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;wDAAK,WAAU;kEAAW;;;;;;;;;;;;0DAE7B,8OAAC;gDACC,WAAU;gDACV,SAAS,CAAC;oDACR,EAAE,eAAe;oDACjB,kBAAkB,CAAC,OACjB,KAAK,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;gDAEhC;0DAEA,cAAA,8OAAC,4LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;;uCAjBV;;;;;;;;;qDAuBX,8OAAC;gCAAI,WAAU;0CAA6B;;;;;;;;;;;;kCAMhD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAA2B;;;;;;0CAC1C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;gDAAK,WAAU;0DAAU;;;;;;;;;;;;kDAE5B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,8OAAC;gDAAK,WAAU;0DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQnC,qBAAqB,8BACpB,8OAAC,8IAAA,CAAA,UAAa;gBACZ,QAAQ;gBACR,cAAc,CAAC,OAAS,qBAAqB;gBAC7C,MAAM;gBACN,cAAc,cAAc,OAAO,aAAa;gBAChD,QAAQ;oBACN,8BAA8B;oBAC9B,qBAAqB;gBACvB;gBACA,QAAQ;oBACN,8BAA8B;oBAC9B,qBAAqB;gBACvB;;;;;;YAKH,6BACC,8OAAC,kIAAA,CAAA,UAAY;gBACX,QAAQ;gBACR,SAAS,IAAM,oBAAoB;gBACnC,QAAQ,YAAY,EAAE;;;;;;0BAK1B,8OAAC,gJAAA,CAAA,UAAiB;gBAChB,QAAQ;gBACR,cAAc;;;;;;YAIf,kBAAkB,eAAe,6BAChC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAO,WAAU;0CAA+E;;;;;;0CAGjG,8OAAC;gCAAO,WAAU;0CAAwE;;;;;;0CAG1F,8OAAC;gCAAO,WAAU;0CAAwE;;;;;;0CAG1F,8OAAC;gCAAO,WAAU;0CAAwE;;;;;;;;;;;;oBAM3F,iCACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;wCAA2B;wCAC5B;wCACX,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD,EAAE,wBAAwB,UAAU;wCAAgB;;;;;;;gCAG5D,kCACC,8OAAC;oCACC,WAAU;oCACV,SAAS,IAAM,gBAAgB;8CAE/B,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KAAK,kBAAkB,iBAAiB;oDACxC,KAAK,kBAAkB,QAAQ;oDAC/B,OAAO;oDACP,QAAQ;oDACR,WAAU;;;;;;;;;;;0DAGd,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEACZ,kBAAkB,QAAQ;;;;;;kEAE7B,8OAAC;wDAAI,WAAU;;4DACZ,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD,EAAE,wBACL,UACA;4DAAgB;4DAClB;0EACF,8OAAC;gEAAK,WAAU;0EACb,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD,EAAE,wBACL,kBAAkB,KAAK,IAAI,uBAC3B,kBAAkB,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;yDAO3C,8OAAC;oCAAI,WAAU;;wCAA6B;wCACR;wCACjC,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD,EAAE,wBAAwB,UAAU;wCAAiB;wCAAI;;;;;;;;;;;;;;;;;;oBAQzE,iBAAiB,MAAM,GAAG,mBACzB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;oCAA0B;oCAC9B;oCACR,iBAAiB,MAAM,GAAG,KACvB,UACA,CAAC,CAAC,EAAE,iBAAiB,MAAM,CAAC,CAAC,CAAC;;;;;;;4BAGnC,iBAAiB,GAAG,CAAC,CAAC;gCACrB,yDAAyD;gCACzD,IAAI,QAAQ,EAAE,KAAK,WAAW;oCAC5B,qBACE,8OAAC;wCAEC,WAAU;kDAEV,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;uCARjB;;;;;gCAcV;gCAEA,sCAAsC;gCACtC,MAAM,UAAU,QAAQ,OAAO;gCAC/B,MAAM,eAAe,QAAQ,WAAW;gCACxC,MAAM,aAAa,qBAAqB,WAAW;gCACnD,MAAM,aAAa,aAAa,OAAO,CAAC;gCAExC,oCAAoC;gCACpC,MAAM,aACJ,cAAc,IAAI,QAAQ,SAAS,CAAC,GAAG,cAAc;gCACvD,MAAM,kBACJ,cAAc,IACV,QAAQ,SAAS,CACf,YACA,aAAa,qBAAqB,MAAM,IAE1C;gCACN,MAAM,YACJ,cAAc,IACV,QAAQ,SAAS,CACf,aAAa,qBAAqB,MAAM,IAE1C;gCAEN,0BAA0B;gCAC1B,MAAM,cAAc,IAAI,KAAK,QAAQ,IAAI;gCACzC,MAAM,MAAM,IAAI;gCAChB,MAAM,WAAW,IAAI,OAAO,KAAK,YAAY,OAAO;gCACpD,MAAM,gBAAgB,KAAK,KAAK,CAAC,WAAW,CAAC,OAAO,EAAE;gCACtD,MAAM,cAAc,KAAK,KAAK,CAAC,WAAW,CAAC,OAAO,KAAK,EAAE;gCAEzD,IAAI,cAAc,QAAQ,IAAI;gCAC9B,IAAI,gBAAgB,IAAI;oCACtB,cAAc,GAAG,cAAc,KAAK,CAAC;gCACvC,OAAO,IAAI,cAAc,IAAI;oCAC3B,cAAc,GAAG,YAAY,IAAI,CAAC;gCACpC,OAAO,IAAI,cAAc,IAAI;oCAC3B,cAAc;gCAChB,OAAO;oCACL,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;oCAC5C,cAAc,GAAG,WAAW,KAAK,CAAC;gCACpC;gCAEA,qBACE,8OAAC;oCAEC,WAAU;oCACV,SAAS,OAAO;wCACd,EAAE,cAAc;wCAChB,EAAE,eAAe;wCAEjB,sDAAsD;wCACtD,IAAI,eAA8B;wCAElC,2CAA2C;wCAC3C,qFAAqF;wCACrF,MAAM,gBAAgB,QAAQ,cAAc,EAAE;wCAE9C,IAAI,eAAe;4CACjB,eAAe;wCACjB,OAAO;4CACL,0EAA0E;4CAC1E,MAAM,WAAW,QAAQ,MAAM,CAAC,EAAE;4CAClC,IAAI,YAAY,aAAa,aAAa,IAAI;gDAC5C,eAAe;4CACjB;wCACF;wCAEA,IAAI,cAAc;4CAChB,IAAI;gDACF,2BAA2B;gDAC3B,MAAM,WAAW;gDAEjB,iDAAiD;gDACjD,8DAA8D;gDAC9D,wCAAwC;gDACxC,MAAM,UAAU,MAAM,SAAS,UAAU;gDAEzC,qCAAqC;gDACrC;gDAEA,IAAI,SAAS;oDACX,8BAA8B;oDAC9B,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oDACd,OAAO,IAAI,CAAC;gDACd,OAAO;oDACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gDACd;4CACF,EAAE,OAAO,OAAO;gDACd,QAAQ,KAAK,CAAC,uBAAuB;gDACrC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gDACZ,yCAAyC;gDACzC;4CACF;wCACF,OAAO;4CACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;wCACd;oCACF;;sDAEA,8OAAC;4CAAI,WAAU;sDACZ,aAAa,CAAC,QAAQ,MAAM,CAAC,EAAE,CAAC,EAAE,UAC/B,qBACJ,OAAO,aAAa,CAAC,QAAQ,MAAM,CAAC,EAAE,CAAC,EAAE,UACrC,sBAAsB,YAC1B,aAAa,CACX,QAAQ,MAAM,CAAC,EAAE,CAClB,EAAE,UAAU,mBAAmB,WAAW,mBACzC,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KACE,aAAa,CAAC,QAAQ,MAAM,CAAC,EAAE,CAAC,EAAE,UAC9B,qBACJ;gDAEF,KACE,aAAa,CAAC,QAAQ,MAAM,CAAC,EAAE,CAAC,EAAE,UAC9B,YAAY;gDAElB,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;qEAGZ,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;8DACE,CACC,aAAa,CAAC,QAAQ,MAAM,CAAC,EAAE,CAAC,EAAE,UAC9B,YAAY,YAClB,EAAE,MAAM,CAAC;;;;;;;;;;;;;;;;sDAKjB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACZ,aAAa,CAAC,QAAQ,MAAM,CAAC,EAAE,CAAC,EAAE,UAC/B,YAAY;;;;;;0EAElB,8OAAC;gEAAI,WAAU;0EACZ;;;;;;;;;;;;kEAGL,8OAAC;wDAAI,WAAU;kEACZ,cAAc,kBACb,8OAAC;;8EACC,8OAAC;oEAAK,WAAU;;wEACb,aAAa,CAAC,QAAQ,MAAM,CAAC,EAAE,CAAC,EAAE,UAC/B,YAAY;wEAAa;wEAC3B;;;;;;;gEAEH;8EACD,8OAAC;oEAAK,WAAU;8EACb;;;;;;gEAEF;;;;;;iFAGH,8OAAC;;8EACC,8OAAC;oEAAK,WAAU;;wEACb,aAAa,CAAC,QAAQ,MAAM,CAAC,EAAE,CAAC,EAAE,UAC/B,YAAY;wEAAa;wEAC3B;;;;;;;gEAEH;;;;;;;;;;;;;;;;;;;;;;;;mCAvHN,QAAQ,EAAE;;;;;4BA+HrB;;;;;;;oBAIH,iCACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;+BAKvC,gBAAgB,MAAM,GAAG,mBACvB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;wCAAsB;wCAC1B,gBAAgB,MAAM;wCAAC;;;;;;;8CAElC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAO,WAAU;sDAA6C;;;;;;sDAG/D,8OAAC,gNAAA,CAAA,iBAAc;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;oBAOnC,gBAAgB,MAAM,GAAG,mBACxB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CAAwB;;;;;;;;;;;4BAIxC,OAAO,IAAI,CAAC,iBACV,IAAI,GACJ,GAAG,CAAC,CAAC,uBACJ,8OAAC;;sDAEC,8OAAC;4CAAI,WAAU;sDACZ;;;;;;wCAIF,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,uBAC5B,8OAAC;gDAEC,WAAU;gDACV,SAAS,IACP,gBAAgB;wDACd,IAAI,OAAO,EAAE;wDACb,UAAU,OAAO,QAAQ;wDACzB,mBAAmB,OAAO,iBAAiB;wDAC3C,aAAa,OAAO,WAAW,IAAI;wDACnC,OAAO,OAAO,KAAK,IAAI;oDACzB;;kEAGF,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oEACJ,KACE,OAAO,iBAAiB,IACxB,OAAO,OAAO,iBAAiB,KAAK,YACpC,OAAO,iBAAiB,EAAE,WAAW,KACjC,OAAO,iBAAiB,GACxB;oEAEN,KAAK,OAAO,QAAQ;oEACpB,OAAO;oEACP,QAAQ;oEACR,WAAU;;;;;;;;;;;0EAGd,8OAAC;gEAAK,WAAU;0EAAW,OAAO,QAAQ;;;;;;;;;;;;kEAE5C,8OAAC;wDACC,WAAU;wDACV,SAAS,CAAC;4DACR,EAAE,eAAe;wDACjB,0BAA0B;wDAC5B;kEAEA,cAAA,8OAAC,gNAAA,CAAA,iBAAc;4DAAC,WAAU;;;;;;;;;;;;+CArCvB,OAAO,EAAE;;;;;;mCATV;;;;;;;;;;;oBAwDjB,eACC,iBAAiB,MAAM,KAAK,KAC5B,gBAAgB,MAAM,KAAK,mBACzB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;0CAK1B,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;0CACrC,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;;;;;;;;;;;;;AAS1D", "debugId": null}}, {"offset": {"line": 4023, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/app/%28protected%29/dashboard/contact/page.tsx"], "sourcesContent": ["\"use client\";\r\nimport { useState, useMemo, useEffect } from \"react\";\r\nimport { Users, UserPlus, UsersRound } from \"lucide-react\";\r\nimport ContactSidebar from \"@/components/contact/ContactSidebar\";\r\nimport ContactList from \"@/components/contact/ContactList\";\r\nimport GroupList from \"@/components/contact/GroupList\";\r\nimport FriendRequests from \"@/components/contact/FriendRequests\";\r\nimport SearchHeader from \"@/components/SearchHeader\";\r\nimport { useFriendStore } from \"@/stores/friendStore\";\r\nimport { getUserGroups } from \"@/actions/group.action\";\r\nimport { toast } from \"sonner\";\r\nimport { Group, GroupMember } from \"@/types/base\";\r\n\r\n// Define types for UI components\r\ntype GroupItem = {\r\n  id: string;\r\n  name: string;\r\n  memberCount: number;\r\n  imageUrl: string;\r\n  avatarUrl?: string | null;\r\n  members?: GroupMember[];\r\n};\r\n\r\n// Export the component directly\r\nexport default function ContactPage() {\r\n  const [activeTab, setActiveTab] = useState<string>(\"friends\");\r\n\r\n  // State for storing real group data from API\r\n  const [userGroups, setUserGroups] = useState<GroupItem[]>([]);\r\n\r\n  // Get friend data from store\r\n  const {\r\n    friends,\r\n    receivedRequests,\r\n    sentRequests,\r\n    fetchFriends,\r\n    fetchReceivedRequests,\r\n    fetchSentRequests,\r\n    markFriendRequestsAsRead,\r\n  } = useFriendStore();\r\n\r\n  // Fetch user groups from API\r\n  const fetchUserGroups = async () => {\r\n    try {\r\n      const result = await getUserGroups();\r\n      if (result.success && result.groups) {\r\n        // Transform the data to match the GroupItem type\r\n        const formattedGroups = result.groups.map((group: Group) => ({\r\n          id: group.id,\r\n          name: group.name,\r\n          memberCount: group.members?.length || 0,\r\n          imageUrl: group.avatarUrl || \"\",\r\n          avatarUrl: group.avatarUrl,\r\n          members: group.members,\r\n        }));\r\n        setUserGroups(formattedGroups);\r\n      } else {\r\n        console.error(\"Failed to fetch groups:\", result.error);\r\n        toast.error(\"Không thể tải danh sách nhóm\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error fetching groups:\", error);\r\n      toast.error(\"Đã xảy ra lỗi khi tải danh sách nhóm\");\r\n    }\r\n  };\r\n\r\n  // Fetch data when component mounts\r\n  useEffect(() => {\r\n    fetchFriends();\r\n    fetchReceivedRequests();\r\n    fetchSentRequests();\r\n    fetchUserGroups();\r\n\r\n    // Mark friend requests as read if we're on the requests tab\r\n    if (activeTab === \"requests\") {\r\n      markFriendRequestsAsRead();\r\n    }\r\n  }, [\r\n    fetchFriends,\r\n    fetchReceivedRequests,\r\n    fetchSentRequests,\r\n    activeTab,\r\n    markFriendRequestsAsRead,\r\n  ]);\r\n\r\n  // Get title and count based on active tab\r\n  const { title, count } = useMemo(() => {\r\n    switch (activeTab) {\r\n      case \"friends\":\r\n        return { title: \"Bạn bè\", count: friends.length };\r\n      case \"groups\":\r\n        return { title: \"Nhóm và cộng đồng\", count: userGroups.length };\r\n      case \"requests\":\r\n        return {\r\n          title: \"Lời mời kết bạn\",\r\n          count: receivedRequests.length + sentRequests.length,\r\n        };\r\n\r\n      default:\r\n        return { title: \"Danh bạ\", count: 0 };\r\n    }\r\n  }, [\r\n    activeTab,\r\n    friends.length,\r\n    receivedRequests.length,\r\n    sentRequests.length,\r\n    userGroups.length,\r\n  ]);\r\n\r\n  // Map tab IDs to their corresponding icons\r\n  const tabIcons = useMemo(\r\n    () => ({\r\n      friends: Users,\r\n      groups: UsersRound,\r\n      requests: UserPlus,\r\n    }),\r\n    [],\r\n  );\r\n\r\n  // Render content based on active tab\r\n  const renderContent = () => {\r\n    switch (activeTab) {\r\n      case \"friends\":\r\n        return <ContactList friends={friends} />;\r\n      case \"groups\":\r\n        return <GroupList groups={userGroups} />;\r\n      case \"requests\":\r\n        return (\r\n          <FriendRequests\r\n            receivedRequests={receivedRequests}\r\n            sentRequests={sentRequests}\r\n          />\r\n        );\r\n\r\n      default:\r\n        return null;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex flex-col h-screen w-full overflow-hidden\">\r\n      <div className=\"flex bg-white \">\r\n        <SearchHeader />\r\n        <div className=\"flex-1 p-4 border-b\">\r\n          <div className=\"flex items-center pt-2 gap-2\">\r\n            {(() => {\r\n              const IconComponent =\r\n                tabIcons[activeTab as keyof typeof tabIcons];\r\n              return IconComponent ? (\r\n                <IconComponent className=\"h-5 w-5\" />\r\n              ) : null;\r\n            })()}\r\n            <h1 className=\"text-sm font-semibold\">{title}</h1>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"flex flex-1 bg-[#f0f2f5] overflow-hidden\">\r\n        {/* Left Sidebar - Contact Tabs */}\r\n        <ContactSidebar activeTab={activeTab} setActiveTab={setActiveTab} />\r\n\r\n        <div className=\"flex flex-col flex-1 p-4 overflow-hidden\">\r\n          {/* Dynamic count display based on active tab */}\r\n          <div className=\"text-sm font-semibold text-gray-700 mb-6 mt-2 -px-1 \">\r\n            {title} ({count})\r\n          </div>\r\n\r\n          {/* Main Content - Dynamic based on active tab */}\r\n          <div className=\"flex-1 overflow-auto no-scrollbar\">\r\n            {renderContent()}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;;;;;AAwBe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEnD,6CAA6C;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IAE5D,6BAA6B;IAC7B,MAAM,EACJ,OAAO,EACP,gBAAgB,EAChB,YAAY,EACZ,YAAY,EACZ,qBAAqB,EACrB,iBAAiB,EACjB,wBAAwB,EACzB,GAAG,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD;IAEjB,6BAA6B;IAC7B,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,iIAAA,CAAA,gBAAa,AAAD;YACjC,IAAI,OAAO,OAAO,IAAI,OAAO,MAAM,EAAE;gBACnC,iDAAiD;gBACjD,MAAM,kBAAkB,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,QAAiB,CAAC;wBAC3D,IAAI,MAAM,EAAE;wBACZ,MAAM,MAAM,IAAI;wBAChB,aAAa,MAAM,OAAO,EAAE,UAAU;wBACtC,UAAU,MAAM,SAAS,IAAI;wBAC7B,WAAW,MAAM,SAAS;wBAC1B,SAAS,MAAM,OAAO;oBACxB,CAAC;gBACD,cAAc;YAChB,OAAO;gBACL,QAAQ,KAAK,CAAC,2BAA2B,OAAO,KAAK;gBACrD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,mCAAmC;IACnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QACA;QACA;QACA;QAEA,4DAA4D;QAC5D,IAAI,cAAc,YAAY;YAC5B;QACF;IACF,GAAG;QACD;QACA;QACA;QACA;QACA;KACD;IAED,0CAA0C;IAC1C,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC/B,OAAQ;YACN,KAAK;gBACH,OAAO;oBAAE,OAAO;oBAAU,OAAO,QAAQ,MAAM;gBAAC;YAClD,KAAK;gBACH,OAAO;oBAAE,OAAO;oBAAqB,OAAO,WAAW,MAAM;gBAAC;YAChE,KAAK;gBACH,OAAO;oBACL,OAAO;oBACP,OAAO,iBAAiB,MAAM,GAAG,aAAa,MAAM;gBACtD;YAEF;gBACE,OAAO;oBAAE,OAAO;oBAAW,OAAO;gBAAE;QACxC;IACF,GAAG;QACD;QACA,QAAQ,MAAM;QACd,iBAAiB,MAAM;QACvB,aAAa,MAAM;QACnB,WAAW,MAAM;KAClB;IAED,2CAA2C;IAC3C,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EACrB,IAAM,CAAC;YACL,SAAS,oMAAA,CAAA,QAAK;YACd,QAAQ,kNAAA,CAAA,aAAU;YAClB,UAAU,8MAAA,CAAA,WAAQ;QACpB,CAAC,GACD,EAAE;IAGJ,qCAAqC;IACrC,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,4IAAA,CAAA,UAAW;oBAAC,SAAS;;;;;;YAC/B,KAAK;gBACH,qBAAO,8OAAC,0IAAA,CAAA,UAAS;oBAAC,QAAQ;;;;;;YAC5B,KAAK;gBACH,qBACE,8OAAC,+IAAA,CAAA,UAAc;oBACb,kBAAkB;oBAClB,cAAc;;;;;;YAIpB;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kIAAA,CAAA,UAAY;;;;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;gCACZ,CAAC;oCACA,MAAM,gBACJ,QAAQ,CAAC,UAAmC;oCAC9C,OAAO,8BACL,8OAAC;wCAAc,WAAU;;;;;+CACvB;gCACN,CAAC;8CACD,8OAAC;oCAAG,WAAU;8CAAyB;;;;;;;;;;;;;;;;;;;;;;;0BAK7C,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,+IAAA,CAAA,UAAc;wBAAC,WAAW;wBAAW,cAAc;;;;;;kCAEpD,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;oCACZ;oCAAM;oCAAG;oCAAM;;;;;;;0CAIlB,8OAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}]}