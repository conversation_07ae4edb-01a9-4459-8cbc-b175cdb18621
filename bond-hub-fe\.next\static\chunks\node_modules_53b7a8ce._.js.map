{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "users-round.js", "sources": ["file://D%3A/testnext/bond-hub-fe/node_modules/lucide-react/src/icons/users-round.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M18 21a8 8 0 0 0-16 0', key: '3ypg7q' }],\n  ['circle', { cx: '10', cy: '8', r: '5', key: 'o932ke' }],\n  ['path', { d: 'M22 20c0-3.37-2-6.5-4-8a5 5 0 0 0-.45-8.3', key: '10s06x' }],\n];\n\n/**\n * @component @name UsersRound\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTggMjFhOCA4IDAgMCAwLTE2IDAiIC8+CiAgPGNpcmNsZSBjeD0iMTAiIGN5PSI4IiByPSI1IiAvPgogIDxwYXRoIGQ9Ik0yMiAyMGMwLTMuMzctMi02LjUtNC04YTUgNSAwIDAgMC0uNDUtOC4zIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/users-round\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst UsersRound = createLucideIcon('users-round', __iconNode);\n\nexport default UsersRound;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAyB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IACtD;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,GAAA,CAAK;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA,CAAA;IACvD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA6C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC5E,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAa,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAiB,AAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "file": "search.js", "sources": ["file://D%3A/testnext/bond-hub-fe/node_modules/lucide-react/src/icons/search.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '11', cy: '11', r: '8', key: '4ej97u' }],\n  ['path', { d: 'm21 21-4.3-4.3', key: '1qie3q' }],\n];\n\n/**\n * @component @name Search\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMSIgY3k9IjExIiByPSI4IiAvPgogIDxwYXRoIGQ9Im0yMSAyMS00LjMtNC4zIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/search\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Search = createLucideIcon('search', __iconNode);\n\nexport default Search;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA,CAAA;IACxD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAkB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CACjD,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "file": "ellipsis.js", "sources": ["file://D%3A/testnext/bond-hub-fe/node_modules/lucide-react/src/icons/ellipsis.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '1', key: '41hilf' }],\n  ['circle', { cx: '19', cy: '12', r: '1', key: '1wjl8i' }],\n  ['circle', { cx: '5', cy: '12', r: '1', key: '1pcz8c' }],\n];\n\n/**\n * @component @name Ellipsis\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxIiAvPgogIDxjaXJjbGUgY3g9IjE5IiBjeT0iMTIiIHI9IjEiIC8+CiAgPGNpcmNsZSBjeD0iNSIgY3k9IjEyIiByPSIxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/ellipsis\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Ellipsis = createLucideIcon('ellipsis', __iconNode);\n\nexport default Ellipsis;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA,CAAA;IACxD;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA,CAAA;IACxD;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;CACzD,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 169, "column": 0}, "map": {"version": 3, "file": "clock.js", "sources": ["file://D%3A/testnext/bond-hub-fe/node_modules/lucide-react/src/icons/clock.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['polyline', { points: '12 6 12 12 16 14', key: '68esgv' }],\n];\n\n/**\n * @component @name Clock\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8cG9seWxpbmUgcG9pbnRzPSIxMiA2IDEyIDEyIDE2IDE0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/clock\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Clock = createLucideIcon('clock', __iconNode);\n\nexport default Clock;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA,CAAA;IACzD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;QAAA,CAAA;YAAE,QAAQ,CAAoB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC5D,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 217, "column": 0}, "map": {"version": 3, "file": "smile-plus.js", "sources": ["file://D%3A/testnext/bond-hub-fe/node_modules/lucide-react/src/icons/smile-plus.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M22 11v1a10 10 0 1 1-9-10', key: 'ew0xw9' }],\n  ['path', { d: 'M8 14s1.5 2 4 2 4-2 4-2', key: '1y1vjs' }],\n  ['line', { x1: '9', x2: '9.01', y1: '9', y2: '9', key: 'yxxnd0' }],\n  ['line', { x1: '15', x2: '15.01', y1: '9', y2: '9', key: '1p4y9e' }],\n  ['path', { d: 'M16 5h6', key: '1vod17' }],\n  ['path', { d: 'M19 2v6', key: '4bpg5p' }],\n];\n\n/**\n * @component @name SmilePlus\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjIgMTF2MWExMCAxMCAwIDEgMS05LTEwIiAvPgogIDxwYXRoIGQ9Ik04IDE0czEuNSAyIDQgMiA0LTIgNC0yIiAvPgogIDxsaW5lIHgxPSI5IiB4Mj0iOS4wMSIgeTE9IjkiIHkyPSI5IiAvPgogIDxsaW5lIHgxPSIxNSIgeDI9IjE1LjAxIiB5MT0iOSIgeTI9IjkiIC8+CiAgPHBhdGggZD0iTTE2IDVoNiIgLz4KICA8cGF0aCBkPSJNMTkgMnY2IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/smile-plus\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst SmilePlus = createLucideIcon('smile-plus', __iconNode);\n\nexport default SmilePlus;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA6B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IAC1D;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA2B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IACxD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAK;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IACjE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAK;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IACnE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC1C,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAY,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,AAAjB,CAAA,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAc,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 297, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/node_modules/qrcode.react/lib/esm/index.js"], "sourcesContent": ["var __defProp = Object.defineProperty;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __objRest = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\n\n// src/index.tsx\nimport React from \"react\";\n\n// src/third-party/qrcodegen/index.ts\n/**\n * @license QR Code generator library (TypeScript)\n * Copyright (c) Project Nayuki.\n * SPDX-License-Identifier: MIT\n */\nvar qrcodegen;\n((qrcodegen2) => {\n  const _QrCode = class _QrCode {\n    /*-- Constructor (low level) and fields --*/\n    // Creates a new QR Code with the given version number,\n    // error correction level, data codeword bytes, and mask number.\n    // This is a low-level API that most users should not use directly.\n    // A mid-level API is the encodeSegments() function.\n    constructor(version, errorCorrectionLevel, dataCodewords, msk) {\n      this.version = version;\n      this.errorCorrectionLevel = errorCorrectionLevel;\n      // The modules of this QR Code (false = light, true = dark).\n      // Immutable after constructor finishes. Accessed through getModule().\n      this.modules = [];\n      // Indicates function modules that are not subjected to masking. Discarded when constructor finishes.\n      this.isFunction = [];\n      if (version < _QrCode.MIN_VERSION || version > _QrCode.MAX_VERSION)\n        throw new RangeError(\"Version value out of range\");\n      if (msk < -1 || msk > 7)\n        throw new RangeError(\"Mask value out of range\");\n      this.size = version * 4 + 17;\n      let row = [];\n      for (let i = 0; i < this.size; i++)\n        row.push(false);\n      for (let i = 0; i < this.size; i++) {\n        this.modules.push(row.slice());\n        this.isFunction.push(row.slice());\n      }\n      this.drawFunctionPatterns();\n      const allCodewords = this.addEccAndInterleave(dataCodewords);\n      this.drawCodewords(allCodewords);\n      if (msk == -1) {\n        let minPenalty = 1e9;\n        for (let i = 0; i < 8; i++) {\n          this.applyMask(i);\n          this.drawFormatBits(i);\n          const penalty = this.getPenaltyScore();\n          if (penalty < minPenalty) {\n            msk = i;\n            minPenalty = penalty;\n          }\n          this.applyMask(i);\n        }\n      }\n      assert(0 <= msk && msk <= 7);\n      this.mask = msk;\n      this.applyMask(msk);\n      this.drawFormatBits(msk);\n      this.isFunction = [];\n    }\n    /*-- Static factory functions (high level) --*/\n    // Returns a QR Code representing the given Unicode text string at the given error correction level.\n    // As a conservative upper bound, this function is guaranteed to succeed for strings that have 738 or fewer\n    // Unicode code points (not UTF-16 code units) if the low error correction level is used. The smallest possible\n    // QR Code version is automatically chosen for the output. The ECC level of the result may be higher than the\n    // ecl argument if it can be done without increasing the version.\n    static encodeText(text, ecl) {\n      const segs = qrcodegen2.QrSegment.makeSegments(text);\n      return _QrCode.encodeSegments(segs, ecl);\n    }\n    // Returns a QR Code representing the given binary data at the given error correction level.\n    // This function always encodes using the binary segment mode, not any text mode. The maximum number of\n    // bytes allowed is 2953. The smallest possible QR Code version is automatically chosen for the output.\n    // The ECC level of the result may be higher than the ecl argument if it can be done without increasing the version.\n    static encodeBinary(data, ecl) {\n      const seg = qrcodegen2.QrSegment.makeBytes(data);\n      return _QrCode.encodeSegments([seg], ecl);\n    }\n    /*-- Static factory functions (mid level) --*/\n    // Returns a QR Code representing the given segments with the given encoding parameters.\n    // The smallest possible QR Code version within the given range is automatically\n    // chosen for the output. Iff boostEcl is true, then the ECC level of the result\n    // may be higher than the ecl argument if it can be done without increasing the\n    // version. The mask number is either between 0 to 7 (inclusive) to force that\n    // mask, or -1 to automatically choose an appropriate mask (which may be slow).\n    // This function allows the user to create a custom sequence of segments that switches\n    // between modes (such as alphanumeric and byte) to encode text in less space.\n    // This is a mid-level API; the high-level API is encodeText() and encodeBinary().\n    static encodeSegments(segs, ecl, minVersion = 1, maxVersion = 40, mask = -1, boostEcl = true) {\n      if (!(_QrCode.MIN_VERSION <= minVersion && minVersion <= maxVersion && maxVersion <= _QrCode.MAX_VERSION) || mask < -1 || mask > 7)\n        throw new RangeError(\"Invalid value\");\n      let version;\n      let dataUsedBits;\n      for (version = minVersion; ; version++) {\n        const dataCapacityBits2 = _QrCode.getNumDataCodewords(version, ecl) * 8;\n        const usedBits = QrSegment.getTotalBits(segs, version);\n        if (usedBits <= dataCapacityBits2) {\n          dataUsedBits = usedBits;\n          break;\n        }\n        if (version >= maxVersion)\n          throw new RangeError(\"Data too long\");\n      }\n      for (const newEcl of [_QrCode.Ecc.MEDIUM, _QrCode.Ecc.QUARTILE, _QrCode.Ecc.HIGH]) {\n        if (boostEcl && dataUsedBits <= _QrCode.getNumDataCodewords(version, newEcl) * 8)\n          ecl = newEcl;\n      }\n      let bb = [];\n      for (const seg of segs) {\n        appendBits(seg.mode.modeBits, 4, bb);\n        appendBits(seg.numChars, seg.mode.numCharCountBits(version), bb);\n        for (const b of seg.getData())\n          bb.push(b);\n      }\n      assert(bb.length == dataUsedBits);\n      const dataCapacityBits = _QrCode.getNumDataCodewords(version, ecl) * 8;\n      assert(bb.length <= dataCapacityBits);\n      appendBits(0, Math.min(4, dataCapacityBits - bb.length), bb);\n      appendBits(0, (8 - bb.length % 8) % 8, bb);\n      assert(bb.length % 8 == 0);\n      for (let padByte = 236; bb.length < dataCapacityBits; padByte ^= 236 ^ 17)\n        appendBits(padByte, 8, bb);\n      let dataCodewords = [];\n      while (dataCodewords.length * 8 < bb.length)\n        dataCodewords.push(0);\n      bb.forEach((b, i) => dataCodewords[i >>> 3] |= b << 7 - (i & 7));\n      return new _QrCode(version, ecl, dataCodewords, mask);\n    }\n    /*-- Accessor methods --*/\n    // Returns the color of the module (pixel) at the given coordinates, which is false\n    // for light or true for dark. The top left corner has the coordinates (x=0, y=0).\n    // If the given coordinates are out of bounds, then false (light) is returned.\n    getModule(x, y) {\n      return 0 <= x && x < this.size && 0 <= y && y < this.size && this.modules[y][x];\n    }\n    // Modified to expose modules for easy access\n    getModules() {\n      return this.modules;\n    }\n    /*-- Private helper methods for constructor: Drawing function modules --*/\n    // Reads this object's version field, and draws and marks all function modules.\n    drawFunctionPatterns() {\n      for (let i = 0; i < this.size; i++) {\n        this.setFunctionModule(6, i, i % 2 == 0);\n        this.setFunctionModule(i, 6, i % 2 == 0);\n      }\n      this.drawFinderPattern(3, 3);\n      this.drawFinderPattern(this.size - 4, 3);\n      this.drawFinderPattern(3, this.size - 4);\n      const alignPatPos = this.getAlignmentPatternPositions();\n      const numAlign = alignPatPos.length;\n      for (let i = 0; i < numAlign; i++) {\n        for (let j = 0; j < numAlign; j++) {\n          if (!(i == 0 && j == 0 || i == 0 && j == numAlign - 1 || i == numAlign - 1 && j == 0))\n            this.drawAlignmentPattern(alignPatPos[i], alignPatPos[j]);\n        }\n      }\n      this.drawFormatBits(0);\n      this.drawVersion();\n    }\n    // Draws two copies of the format bits (with its own error correction code)\n    // based on the given mask and this object's error correction level field.\n    drawFormatBits(mask) {\n      const data = this.errorCorrectionLevel.formatBits << 3 | mask;\n      let rem = data;\n      for (let i = 0; i < 10; i++)\n        rem = rem << 1 ^ (rem >>> 9) * 1335;\n      const bits = (data << 10 | rem) ^ 21522;\n      assert(bits >>> 15 == 0);\n      for (let i = 0; i <= 5; i++)\n        this.setFunctionModule(8, i, getBit(bits, i));\n      this.setFunctionModule(8, 7, getBit(bits, 6));\n      this.setFunctionModule(8, 8, getBit(bits, 7));\n      this.setFunctionModule(7, 8, getBit(bits, 8));\n      for (let i = 9; i < 15; i++)\n        this.setFunctionModule(14 - i, 8, getBit(bits, i));\n      for (let i = 0; i < 8; i++)\n        this.setFunctionModule(this.size - 1 - i, 8, getBit(bits, i));\n      for (let i = 8; i < 15; i++)\n        this.setFunctionModule(8, this.size - 15 + i, getBit(bits, i));\n      this.setFunctionModule(8, this.size - 8, true);\n    }\n    // Draws two copies of the version bits (with its own error correction code),\n    // based on this object's version field, iff 7 <= version <= 40.\n    drawVersion() {\n      if (this.version < 7)\n        return;\n      let rem = this.version;\n      for (let i = 0; i < 12; i++)\n        rem = rem << 1 ^ (rem >>> 11) * 7973;\n      const bits = this.version << 12 | rem;\n      assert(bits >>> 18 == 0);\n      for (let i = 0; i < 18; i++) {\n        const color = getBit(bits, i);\n        const a = this.size - 11 + i % 3;\n        const b = Math.floor(i / 3);\n        this.setFunctionModule(a, b, color);\n        this.setFunctionModule(b, a, color);\n      }\n    }\n    // Draws a 9*9 finder pattern including the border separator,\n    // with the center module at (x, y). Modules can be out of bounds.\n    drawFinderPattern(x, y) {\n      for (let dy = -4; dy <= 4; dy++) {\n        for (let dx = -4; dx <= 4; dx++) {\n          const dist = Math.max(Math.abs(dx), Math.abs(dy));\n          const xx = x + dx;\n          const yy = y + dy;\n          if (0 <= xx && xx < this.size && 0 <= yy && yy < this.size)\n            this.setFunctionModule(xx, yy, dist != 2 && dist != 4);\n        }\n      }\n    }\n    // Draws a 5*5 alignment pattern, with the center module\n    // at (x, y). All modules must be in bounds.\n    drawAlignmentPattern(x, y) {\n      for (let dy = -2; dy <= 2; dy++) {\n        for (let dx = -2; dx <= 2; dx++)\n          this.setFunctionModule(x + dx, y + dy, Math.max(Math.abs(dx), Math.abs(dy)) != 1);\n      }\n    }\n    // Sets the color of a module and marks it as a function module.\n    // Only used by the constructor. Coordinates must be in bounds.\n    setFunctionModule(x, y, isDark) {\n      this.modules[y][x] = isDark;\n      this.isFunction[y][x] = true;\n    }\n    /*-- Private helper methods for constructor: Codewords and masking --*/\n    // Returns a new byte string representing the given data with the appropriate error correction\n    // codewords appended to it, based on this object's version and error correction level.\n    addEccAndInterleave(data) {\n      const ver = this.version;\n      const ecl = this.errorCorrectionLevel;\n      if (data.length != _QrCode.getNumDataCodewords(ver, ecl))\n        throw new RangeError(\"Invalid argument\");\n      const numBlocks = _QrCode.NUM_ERROR_CORRECTION_BLOCKS[ecl.ordinal][ver];\n      const blockEccLen = _QrCode.ECC_CODEWORDS_PER_BLOCK[ecl.ordinal][ver];\n      const rawCodewords = Math.floor(_QrCode.getNumRawDataModules(ver) / 8);\n      const numShortBlocks = numBlocks - rawCodewords % numBlocks;\n      const shortBlockLen = Math.floor(rawCodewords / numBlocks);\n      let blocks = [];\n      const rsDiv = _QrCode.reedSolomonComputeDivisor(blockEccLen);\n      for (let i = 0, k = 0; i < numBlocks; i++) {\n        let dat = data.slice(k, k + shortBlockLen - blockEccLen + (i < numShortBlocks ? 0 : 1));\n        k += dat.length;\n        const ecc = _QrCode.reedSolomonComputeRemainder(dat, rsDiv);\n        if (i < numShortBlocks)\n          dat.push(0);\n        blocks.push(dat.concat(ecc));\n      }\n      let result = [];\n      for (let i = 0; i < blocks[0].length; i++) {\n        blocks.forEach((block, j) => {\n          if (i != shortBlockLen - blockEccLen || j >= numShortBlocks)\n            result.push(block[i]);\n        });\n      }\n      assert(result.length == rawCodewords);\n      return result;\n    }\n    // Draws the given sequence of 8-bit codewords (data and error correction) onto the entire\n    // data area of this QR Code. Function modules need to be marked off before this is called.\n    drawCodewords(data) {\n      if (data.length != Math.floor(_QrCode.getNumRawDataModules(this.version) / 8))\n        throw new RangeError(\"Invalid argument\");\n      let i = 0;\n      for (let right = this.size - 1; right >= 1; right -= 2) {\n        if (right == 6)\n          right = 5;\n        for (let vert = 0; vert < this.size; vert++) {\n          for (let j = 0; j < 2; j++) {\n            const x = right - j;\n            const upward = (right + 1 & 2) == 0;\n            const y = upward ? this.size - 1 - vert : vert;\n            if (!this.isFunction[y][x] && i < data.length * 8) {\n              this.modules[y][x] = getBit(data[i >>> 3], 7 - (i & 7));\n              i++;\n            }\n          }\n        }\n      }\n      assert(i == data.length * 8);\n    }\n    // XORs the codeword modules in this QR Code with the given mask pattern.\n    // The function modules must be marked and the codeword bits must be drawn\n    // before masking. Due to the arithmetic of XOR, calling applyMask() with\n    // the same mask value a second time will undo the mask. A final well-formed\n    // QR Code needs exactly one (not zero, two, etc.) mask applied.\n    applyMask(mask) {\n      if (mask < 0 || mask > 7)\n        throw new RangeError(\"Mask value out of range\");\n      for (let y = 0; y < this.size; y++) {\n        for (let x = 0; x < this.size; x++) {\n          let invert;\n          switch (mask) {\n            case 0:\n              invert = (x + y) % 2 == 0;\n              break;\n            case 1:\n              invert = y % 2 == 0;\n              break;\n            case 2:\n              invert = x % 3 == 0;\n              break;\n            case 3:\n              invert = (x + y) % 3 == 0;\n              break;\n            case 4:\n              invert = (Math.floor(x / 3) + Math.floor(y / 2)) % 2 == 0;\n              break;\n            case 5:\n              invert = x * y % 2 + x * y % 3 == 0;\n              break;\n            case 6:\n              invert = (x * y % 2 + x * y % 3) % 2 == 0;\n              break;\n            case 7:\n              invert = ((x + y) % 2 + x * y % 3) % 2 == 0;\n              break;\n            default:\n              throw new Error(\"Unreachable\");\n          }\n          if (!this.isFunction[y][x] && invert)\n            this.modules[y][x] = !this.modules[y][x];\n        }\n      }\n    }\n    // Calculates and returns the penalty score based on state of this QR Code's current modules.\n    // This is used by the automatic mask choice algorithm to find the mask pattern that yields the lowest score.\n    getPenaltyScore() {\n      let result = 0;\n      for (let y = 0; y < this.size; y++) {\n        let runColor = false;\n        let runX = 0;\n        let runHistory = [0, 0, 0, 0, 0, 0, 0];\n        for (let x = 0; x < this.size; x++) {\n          if (this.modules[y][x] == runColor) {\n            runX++;\n            if (runX == 5)\n              result += _QrCode.PENALTY_N1;\n            else if (runX > 5)\n              result++;\n          } else {\n            this.finderPenaltyAddHistory(runX, runHistory);\n            if (!runColor)\n              result += this.finderPenaltyCountPatterns(runHistory) * _QrCode.PENALTY_N3;\n            runColor = this.modules[y][x];\n            runX = 1;\n          }\n        }\n        result += this.finderPenaltyTerminateAndCount(runColor, runX, runHistory) * _QrCode.PENALTY_N3;\n      }\n      for (let x = 0; x < this.size; x++) {\n        let runColor = false;\n        let runY = 0;\n        let runHistory = [0, 0, 0, 0, 0, 0, 0];\n        for (let y = 0; y < this.size; y++) {\n          if (this.modules[y][x] == runColor) {\n            runY++;\n            if (runY == 5)\n              result += _QrCode.PENALTY_N1;\n            else if (runY > 5)\n              result++;\n          } else {\n            this.finderPenaltyAddHistory(runY, runHistory);\n            if (!runColor)\n              result += this.finderPenaltyCountPatterns(runHistory) * _QrCode.PENALTY_N3;\n            runColor = this.modules[y][x];\n            runY = 1;\n          }\n        }\n        result += this.finderPenaltyTerminateAndCount(runColor, runY, runHistory) * _QrCode.PENALTY_N3;\n      }\n      for (let y = 0; y < this.size - 1; y++) {\n        for (let x = 0; x < this.size - 1; x++) {\n          const color = this.modules[y][x];\n          if (color == this.modules[y][x + 1] && color == this.modules[y + 1][x] && color == this.modules[y + 1][x + 1])\n            result += _QrCode.PENALTY_N2;\n        }\n      }\n      let dark = 0;\n      for (const row of this.modules)\n        dark = row.reduce((sum, color) => sum + (color ? 1 : 0), dark);\n      const total = this.size * this.size;\n      const k = Math.ceil(Math.abs(dark * 20 - total * 10) / total) - 1;\n      assert(0 <= k && k <= 9);\n      result += k * _QrCode.PENALTY_N4;\n      assert(0 <= result && result <= 2568888);\n      return result;\n    }\n    /*-- Private helper functions --*/\n    // Returns an ascending list of positions of alignment patterns for this version number.\n    // Each position is in the range [0,177), and are used on both the x and y axes.\n    // This could be implemented as lookup table of 40 variable-length lists of integers.\n    getAlignmentPatternPositions() {\n      if (this.version == 1)\n        return [];\n      else {\n        const numAlign = Math.floor(this.version / 7) + 2;\n        const step = this.version == 32 ? 26 : Math.ceil((this.version * 4 + 4) / (numAlign * 2 - 2)) * 2;\n        let result = [6];\n        for (let pos = this.size - 7; result.length < numAlign; pos -= step)\n          result.splice(1, 0, pos);\n        return result;\n      }\n    }\n    // Returns the number of data bits that can be stored in a QR Code of the given version number, after\n    // all function modules are excluded. This includes remainder bits, so it might not be a multiple of 8.\n    // The result is in the range [208, 29648]. This could be implemented as a 40-entry lookup table.\n    static getNumRawDataModules(ver) {\n      if (ver < _QrCode.MIN_VERSION || ver > _QrCode.MAX_VERSION)\n        throw new RangeError(\"Version number out of range\");\n      let result = (16 * ver + 128) * ver + 64;\n      if (ver >= 2) {\n        const numAlign = Math.floor(ver / 7) + 2;\n        result -= (25 * numAlign - 10) * numAlign - 55;\n        if (ver >= 7)\n          result -= 36;\n      }\n      assert(208 <= result && result <= 29648);\n      return result;\n    }\n    // Returns the number of 8-bit data (i.e. not error correction) codewords contained in any\n    // QR Code of the given version number and error correction level, with remainder bits discarded.\n    // This stateless pure function could be implemented as a (40*4)-cell lookup table.\n    static getNumDataCodewords(ver, ecl) {\n      return Math.floor(_QrCode.getNumRawDataModules(ver) / 8) - _QrCode.ECC_CODEWORDS_PER_BLOCK[ecl.ordinal][ver] * _QrCode.NUM_ERROR_CORRECTION_BLOCKS[ecl.ordinal][ver];\n    }\n    // Returns a Reed-Solomon ECC generator polynomial for the given degree. This could be\n    // implemented as a lookup table over all possible parameter values, instead of as an algorithm.\n    static reedSolomonComputeDivisor(degree) {\n      if (degree < 1 || degree > 255)\n        throw new RangeError(\"Degree out of range\");\n      let result = [];\n      for (let i = 0; i < degree - 1; i++)\n        result.push(0);\n      result.push(1);\n      let root = 1;\n      for (let i = 0; i < degree; i++) {\n        for (let j = 0; j < result.length; j++) {\n          result[j] = _QrCode.reedSolomonMultiply(result[j], root);\n          if (j + 1 < result.length)\n            result[j] ^= result[j + 1];\n        }\n        root = _QrCode.reedSolomonMultiply(root, 2);\n      }\n      return result;\n    }\n    // Returns the Reed-Solomon error correction codeword for the given data and divisor polynomials.\n    static reedSolomonComputeRemainder(data, divisor) {\n      let result = divisor.map((_) => 0);\n      for (const b of data) {\n        const factor = b ^ result.shift();\n        result.push(0);\n        divisor.forEach((coef, i) => result[i] ^= _QrCode.reedSolomonMultiply(coef, factor));\n      }\n      return result;\n    }\n    // Returns the product of the two given field elements modulo GF(2^8/0x11D). The arguments and result\n    // are unsigned 8-bit integers. This could be implemented as a lookup table of 256*256 entries of uint8.\n    static reedSolomonMultiply(x, y) {\n      if (x >>> 8 != 0 || y >>> 8 != 0)\n        throw new RangeError(\"Byte out of range\");\n      let z = 0;\n      for (let i = 7; i >= 0; i--) {\n        z = z << 1 ^ (z >>> 7) * 285;\n        z ^= (y >>> i & 1) * x;\n      }\n      assert(z >>> 8 == 0);\n      return z;\n    }\n    // Can only be called immediately after a light run is added, and\n    // returns either 0, 1, or 2. A helper function for getPenaltyScore().\n    finderPenaltyCountPatterns(runHistory) {\n      const n = runHistory[1];\n      assert(n <= this.size * 3);\n      const core = n > 0 && runHistory[2] == n && runHistory[3] == n * 3 && runHistory[4] == n && runHistory[5] == n;\n      return (core && runHistory[0] >= n * 4 && runHistory[6] >= n ? 1 : 0) + (core && runHistory[6] >= n * 4 && runHistory[0] >= n ? 1 : 0);\n    }\n    // Must be called at the end of a line (row or column) of modules. A helper function for getPenaltyScore().\n    finderPenaltyTerminateAndCount(currentRunColor, currentRunLength, runHistory) {\n      if (currentRunColor) {\n        this.finderPenaltyAddHistory(currentRunLength, runHistory);\n        currentRunLength = 0;\n      }\n      currentRunLength += this.size;\n      this.finderPenaltyAddHistory(currentRunLength, runHistory);\n      return this.finderPenaltyCountPatterns(runHistory);\n    }\n    // Pushes the given value to the front and drops the last value. A helper function for getPenaltyScore().\n    finderPenaltyAddHistory(currentRunLength, runHistory) {\n      if (runHistory[0] == 0)\n        currentRunLength += this.size;\n      runHistory.pop();\n      runHistory.unshift(currentRunLength);\n    }\n  };\n  /*-- Constants and tables --*/\n  // The minimum version number supported in the QR Code Model 2 standard.\n  _QrCode.MIN_VERSION = 1;\n  // The maximum version number supported in the QR Code Model 2 standard.\n  _QrCode.MAX_VERSION = 40;\n  // For use in getPenaltyScore(), when evaluating which mask is best.\n  _QrCode.PENALTY_N1 = 3;\n  _QrCode.PENALTY_N2 = 3;\n  _QrCode.PENALTY_N3 = 40;\n  _QrCode.PENALTY_N4 = 10;\n  _QrCode.ECC_CODEWORDS_PER_BLOCK = [\n    // Version: (note that index 0 is for padding, and is set to an illegal value)\n    //0,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40    Error correction level\n    [-1, 7, 10, 15, 20, 26, 18, 20, 24, 30, 18, 20, 24, 26, 30, 22, 24, 28, 30, 28, 28, 28, 28, 30, 30, 26, 28, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30],\n    // Low\n    [-1, 10, 16, 26, 18, 24, 16, 18, 22, 22, 26, 30, 22, 22, 24, 24, 28, 28, 26, 26, 26, 26, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28],\n    // Medium\n    [-1, 13, 22, 18, 26, 18, 24, 18, 22, 20, 24, 28, 26, 24, 20, 30, 24, 28, 28, 26, 30, 28, 30, 30, 30, 30, 28, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30],\n    // Quartile\n    [-1, 17, 28, 22, 16, 22, 28, 26, 26, 24, 28, 24, 28, 22, 24, 24, 30, 28, 28, 26, 28, 30, 24, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30]\n    // High\n  ];\n  _QrCode.NUM_ERROR_CORRECTION_BLOCKS = [\n    // Version: (note that index 0 is for padding, and is set to an illegal value)\n    //0, 1, 2, 3, 4, 5, 6, 7, 8, 9,10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40    Error correction level\n    [-1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 4, 4, 4, 4, 4, 6, 6, 6, 6, 7, 8, 8, 9, 9, 10, 12, 12, 12, 13, 14, 15, 16, 17, 18, 19, 19, 20, 21, 22, 24, 25],\n    // Low\n    [-1, 1, 1, 1, 2, 2, 4, 4, 4, 5, 5, 5, 8, 9, 9, 10, 10, 11, 13, 14, 16, 17, 17, 18, 20, 21, 23, 25, 26, 28, 29, 31, 33, 35, 37, 38, 40, 43, 45, 47, 49],\n    // Medium\n    [-1, 1, 1, 2, 2, 4, 4, 6, 6, 8, 8, 8, 10, 12, 16, 12, 17, 16, 18, 21, 20, 23, 23, 25, 27, 29, 34, 34, 35, 38, 40, 43, 45, 48, 51, 53, 56, 59, 62, 65, 68],\n    // Quartile\n    [-1, 1, 1, 2, 4, 4, 4, 5, 6, 8, 8, 11, 11, 16, 16, 18, 16, 19, 21, 25, 25, 25, 34, 30, 32, 35, 37, 40, 42, 45, 48, 51, 54, 57, 60, 63, 66, 70, 74, 77, 81]\n    // High\n  ];\n  let QrCode = _QrCode;\n  qrcodegen2.QrCode = _QrCode;\n  function appendBits(val, len, bb) {\n    if (len < 0 || len > 31 || val >>> len != 0)\n      throw new RangeError(\"Value out of range\");\n    for (let i = len - 1; i >= 0; i--)\n      bb.push(val >>> i & 1);\n  }\n  function getBit(x, i) {\n    return (x >>> i & 1) != 0;\n  }\n  function assert(cond) {\n    if (!cond)\n      throw new Error(\"Assertion error\");\n  }\n  const _QrSegment = class _QrSegment {\n    /*-- Constructor (low level) and fields --*/\n    // Creates a new QR Code segment with the given attributes and data.\n    // The character count (numChars) must agree with the mode and the bit buffer length,\n    // but the constraint isn't checked. The given bit buffer is cloned and stored.\n    constructor(mode, numChars, bitData) {\n      this.mode = mode;\n      this.numChars = numChars;\n      this.bitData = bitData;\n      if (numChars < 0)\n        throw new RangeError(\"Invalid argument\");\n      this.bitData = bitData.slice();\n    }\n    /*-- Static factory functions (mid level) --*/\n    // Returns a segment representing the given binary data encoded in\n    // byte mode. All input byte arrays are acceptable. Any text string\n    // can be converted to UTF-8 bytes and encoded as a byte mode segment.\n    static makeBytes(data) {\n      let bb = [];\n      for (const b of data)\n        appendBits(b, 8, bb);\n      return new _QrSegment(_QrSegment.Mode.BYTE, data.length, bb);\n    }\n    // Returns a segment representing the given string of decimal digits encoded in numeric mode.\n    static makeNumeric(digits) {\n      if (!_QrSegment.isNumeric(digits))\n        throw new RangeError(\"String contains non-numeric characters\");\n      let bb = [];\n      for (let i = 0; i < digits.length; ) {\n        const n = Math.min(digits.length - i, 3);\n        appendBits(parseInt(digits.substring(i, i + n), 10), n * 3 + 1, bb);\n        i += n;\n      }\n      return new _QrSegment(_QrSegment.Mode.NUMERIC, digits.length, bb);\n    }\n    // Returns a segment representing the given text string encoded in alphanumeric mode.\n    // The characters allowed are: 0 to 9, A to Z (uppercase only), space,\n    // dollar, percent, asterisk, plus, hyphen, period, slash, colon.\n    static makeAlphanumeric(text) {\n      if (!_QrSegment.isAlphanumeric(text))\n        throw new RangeError(\"String contains unencodable characters in alphanumeric mode\");\n      let bb = [];\n      let i;\n      for (i = 0; i + 2 <= text.length; i += 2) {\n        let temp = _QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i)) * 45;\n        temp += _QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i + 1));\n        appendBits(temp, 11, bb);\n      }\n      if (i < text.length)\n        appendBits(_QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i)), 6, bb);\n      return new _QrSegment(_QrSegment.Mode.ALPHANUMERIC, text.length, bb);\n    }\n    // Returns a new mutable list of zero or more segments to represent the given Unicode text string.\n    // The result may use various segment modes and switch modes to optimize the length of the bit stream.\n    static makeSegments(text) {\n      if (text == \"\")\n        return [];\n      else if (_QrSegment.isNumeric(text))\n        return [_QrSegment.makeNumeric(text)];\n      else if (_QrSegment.isAlphanumeric(text))\n        return [_QrSegment.makeAlphanumeric(text)];\n      else\n        return [_QrSegment.makeBytes(_QrSegment.toUtf8ByteArray(text))];\n    }\n    // Returns a segment representing an Extended Channel Interpretation\n    // (ECI) designator with the given assignment value.\n    static makeEci(assignVal) {\n      let bb = [];\n      if (assignVal < 0)\n        throw new RangeError(\"ECI assignment value out of range\");\n      else if (assignVal < 1 << 7)\n        appendBits(assignVal, 8, bb);\n      else if (assignVal < 1 << 14) {\n        appendBits(2, 2, bb);\n        appendBits(assignVal, 14, bb);\n      } else if (assignVal < 1e6) {\n        appendBits(6, 3, bb);\n        appendBits(assignVal, 21, bb);\n      } else\n        throw new RangeError(\"ECI assignment value out of range\");\n      return new _QrSegment(_QrSegment.Mode.ECI, 0, bb);\n    }\n    // Tests whether the given string can be encoded as a segment in numeric mode.\n    // A string is encodable iff each character is in the range 0 to 9.\n    static isNumeric(text) {\n      return _QrSegment.NUMERIC_REGEX.test(text);\n    }\n    // Tests whether the given string can be encoded as a segment in alphanumeric mode.\n    // A string is encodable iff each character is in the following set: 0 to 9, A to Z\n    // (uppercase only), space, dollar, percent, asterisk, plus, hyphen, period, slash, colon.\n    static isAlphanumeric(text) {\n      return _QrSegment.ALPHANUMERIC_REGEX.test(text);\n    }\n    /*-- Methods --*/\n    // Returns a new copy of the data bits of this segment.\n    getData() {\n      return this.bitData.slice();\n    }\n    // (Package-private) Calculates and returns the number of bits needed to encode the given segments at\n    // the given version. The result is infinity if a segment has too many characters to fit its length field.\n    static getTotalBits(segs, version) {\n      let result = 0;\n      for (const seg of segs) {\n        const ccbits = seg.mode.numCharCountBits(version);\n        if (seg.numChars >= 1 << ccbits)\n          return Infinity;\n        result += 4 + ccbits + seg.bitData.length;\n      }\n      return result;\n    }\n    // Returns a new array of bytes representing the given string encoded in UTF-8.\n    static toUtf8ByteArray(str) {\n      str = encodeURI(str);\n      let result = [];\n      for (let i = 0; i < str.length; i++) {\n        if (str.charAt(i) != \"%\")\n          result.push(str.charCodeAt(i));\n        else {\n          result.push(parseInt(str.substring(i + 1, i + 3), 16));\n          i += 2;\n        }\n      }\n      return result;\n    }\n  };\n  /*-- Constants --*/\n  // Describes precisely all strings that are encodable in numeric mode.\n  _QrSegment.NUMERIC_REGEX = /^[0-9]*$/;\n  // Describes precisely all strings that are encodable in alphanumeric mode.\n  _QrSegment.ALPHANUMERIC_REGEX = /^[A-Z0-9 $%*+.\\/:-]*$/;\n  // The set of all legal characters in alphanumeric mode,\n  // where each character value maps to the index in the string.\n  _QrSegment.ALPHANUMERIC_CHARSET = \"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:\";\n  let QrSegment = _QrSegment;\n  qrcodegen2.QrSegment = _QrSegment;\n})(qrcodegen || (qrcodegen = {}));\n((qrcodegen2) => {\n  let QrCode;\n  ((QrCode2) => {\n    const _Ecc = class _Ecc {\n      // The QR Code can tolerate about 30% erroneous codewords\n      /*-- Constructor and fields --*/\n      constructor(ordinal, formatBits) {\n        this.ordinal = ordinal;\n        this.formatBits = formatBits;\n      }\n    };\n    /*-- Constants --*/\n    _Ecc.LOW = new _Ecc(0, 1);\n    // The QR Code can tolerate about  7% erroneous codewords\n    _Ecc.MEDIUM = new _Ecc(1, 0);\n    // The QR Code can tolerate about 15% erroneous codewords\n    _Ecc.QUARTILE = new _Ecc(2, 3);\n    // The QR Code can tolerate about 25% erroneous codewords\n    _Ecc.HIGH = new _Ecc(3, 2);\n    let Ecc = _Ecc;\n    QrCode2.Ecc = _Ecc;\n  })(QrCode = qrcodegen2.QrCode || (qrcodegen2.QrCode = {}));\n})(qrcodegen || (qrcodegen = {}));\n((qrcodegen2) => {\n  let QrSegment;\n  ((QrSegment2) => {\n    const _Mode = class _Mode {\n      /*-- Constructor and fields --*/\n      constructor(modeBits, numBitsCharCount) {\n        this.modeBits = modeBits;\n        this.numBitsCharCount = numBitsCharCount;\n      }\n      /*-- Method --*/\n      // (Package-private) Returns the bit width of the character count field for a segment in\n      // this mode in a QR Code at the given version number. The result is in the range [0, 16].\n      numCharCountBits(ver) {\n        return this.numBitsCharCount[Math.floor((ver + 7) / 17)];\n      }\n    };\n    /*-- Constants --*/\n    _Mode.NUMERIC = new _Mode(1, [10, 12, 14]);\n    _Mode.ALPHANUMERIC = new _Mode(2, [9, 11, 13]);\n    _Mode.BYTE = new _Mode(4, [8, 16, 16]);\n    _Mode.KANJI = new _Mode(8, [8, 10, 12]);\n    _Mode.ECI = new _Mode(7, [0, 0, 0]);\n    let Mode = _Mode;\n    QrSegment2.Mode = _Mode;\n  })(QrSegment = qrcodegen2.QrSegment || (qrcodegen2.QrSegment = {}));\n})(qrcodegen || (qrcodegen = {}));\nvar qrcodegen_default = qrcodegen;\n\n// src/index.tsx\n/**\n * @license qrcode.react\n * Copyright (c) Paul O'Shannessy\n * SPDX-License-Identifier: ISC\n */\nvar ERROR_LEVEL_MAP = {\n  L: qrcodegen_default.QrCode.Ecc.LOW,\n  M: qrcodegen_default.QrCode.Ecc.MEDIUM,\n  Q: qrcodegen_default.QrCode.Ecc.QUARTILE,\n  H: qrcodegen_default.QrCode.Ecc.HIGH\n};\nvar DEFAULT_SIZE = 128;\nvar DEFAULT_LEVEL = \"L\";\nvar DEFAULT_BGCOLOR = \"#FFFFFF\";\nvar DEFAULT_FGCOLOR = \"#000000\";\nvar DEFAULT_INCLUDEMARGIN = false;\nvar DEFAULT_MINVERSION = 1;\nvar SPEC_MARGIN_SIZE = 4;\nvar DEFAULT_MARGIN_SIZE = 0;\nvar DEFAULT_IMG_SCALE = 0.1;\nfunction generatePath(modules, margin = 0) {\n  const ops = [];\n  modules.forEach(function(row, y) {\n    let start = null;\n    row.forEach(function(cell, x) {\n      if (!cell && start !== null) {\n        ops.push(\n          `M${start + margin} ${y + margin}h${x - start}v1H${start + margin}z`\n        );\n        start = null;\n        return;\n      }\n      if (x === row.length - 1) {\n        if (!cell) {\n          return;\n        }\n        if (start === null) {\n          ops.push(`M${x + margin},${y + margin} h1v1H${x + margin}z`);\n        } else {\n          ops.push(\n            `M${start + margin},${y + margin} h${x + 1 - start}v1H${start + margin}z`\n          );\n        }\n        return;\n      }\n      if (cell && start === null) {\n        start = x;\n      }\n    });\n  });\n  return ops.join(\"\");\n}\nfunction excavateModules(modules, excavation) {\n  return modules.slice().map((row, y) => {\n    if (y < excavation.y || y >= excavation.y + excavation.h) {\n      return row;\n    }\n    return row.map((cell, x) => {\n      if (x < excavation.x || x >= excavation.x + excavation.w) {\n        return cell;\n      }\n      return false;\n    });\n  });\n}\nfunction getImageSettings(cells, size, margin, imageSettings) {\n  if (imageSettings == null) {\n    return null;\n  }\n  const numCells = cells.length + margin * 2;\n  const defaultSize = Math.floor(size * DEFAULT_IMG_SCALE);\n  const scale = numCells / size;\n  const w = (imageSettings.width || defaultSize) * scale;\n  const h = (imageSettings.height || defaultSize) * scale;\n  const x = imageSettings.x == null ? cells.length / 2 - w / 2 : imageSettings.x * scale;\n  const y = imageSettings.y == null ? cells.length / 2 - h / 2 : imageSettings.y * scale;\n  const opacity = imageSettings.opacity == null ? 1 : imageSettings.opacity;\n  let excavation = null;\n  if (imageSettings.excavate) {\n    let floorX = Math.floor(x);\n    let floorY = Math.floor(y);\n    let ceilW = Math.ceil(w + x - floorX);\n    let ceilH = Math.ceil(h + y - floorY);\n    excavation = { x: floorX, y: floorY, w: ceilW, h: ceilH };\n  }\n  const crossOrigin = imageSettings.crossOrigin;\n  return { x, y, h, w, excavation, opacity, crossOrigin };\n}\nfunction getMarginSize(includeMargin, marginSize) {\n  if (marginSize != null) {\n    return Math.max(Math.floor(marginSize), 0);\n  }\n  return includeMargin ? SPEC_MARGIN_SIZE : DEFAULT_MARGIN_SIZE;\n}\nfunction useQRCode({\n  value,\n  level,\n  minVersion,\n  includeMargin,\n  marginSize,\n  imageSettings,\n  size,\n  boostLevel\n}) {\n  let qrcode = React.useMemo(() => {\n    const values = Array.isArray(value) ? value : [value];\n    const segments = values.reduce((accum, v) => {\n      accum.push(...qrcodegen_default.QrSegment.makeSegments(v));\n      return accum;\n    }, []);\n    return qrcodegen_default.QrCode.encodeSegments(\n      segments,\n      ERROR_LEVEL_MAP[level],\n      minVersion,\n      void 0,\n      void 0,\n      boostLevel\n    );\n  }, [value, level, minVersion, boostLevel]);\n  const { cells, margin, numCells, calculatedImageSettings } = React.useMemo(() => {\n    let cells2 = qrcode.getModules();\n    const margin2 = getMarginSize(includeMargin, marginSize);\n    const numCells2 = cells2.length + margin2 * 2;\n    const calculatedImageSettings2 = getImageSettings(\n      cells2,\n      size,\n      margin2,\n      imageSettings\n    );\n    return {\n      cells: cells2,\n      margin: margin2,\n      numCells: numCells2,\n      calculatedImageSettings: calculatedImageSettings2\n    };\n  }, [qrcode, size, imageSettings, includeMargin, marginSize]);\n  return {\n    qrcode,\n    margin,\n    cells,\n    numCells,\n    calculatedImageSettings\n  };\n}\nvar SUPPORTS_PATH2D = function() {\n  try {\n    new Path2D().addPath(new Path2D());\n  } catch (e) {\n    return false;\n  }\n  return true;\n}();\nvar QRCodeCanvas = React.forwardRef(\n  function QRCodeCanvas2(props, forwardedRef) {\n    const _a = props, {\n      value,\n      size = DEFAULT_SIZE,\n      level = DEFAULT_LEVEL,\n      bgColor = DEFAULT_BGCOLOR,\n      fgColor = DEFAULT_FGCOLOR,\n      includeMargin = DEFAULT_INCLUDEMARGIN,\n      minVersion = DEFAULT_MINVERSION,\n      boostLevel,\n      marginSize,\n      imageSettings\n    } = _a, extraProps = __objRest(_a, [\n      \"value\",\n      \"size\",\n      \"level\",\n      \"bgColor\",\n      \"fgColor\",\n      \"includeMargin\",\n      \"minVersion\",\n      \"boostLevel\",\n      \"marginSize\",\n      \"imageSettings\"\n    ]);\n    const _b = extraProps, { style } = _b, otherProps = __objRest(_b, [\"style\"]);\n    const imgSrc = imageSettings == null ? void 0 : imageSettings.src;\n    const _canvas = React.useRef(null);\n    const _image = React.useRef(null);\n    const setCanvasRef = React.useCallback(\n      (node) => {\n        _canvas.current = node;\n        if (typeof forwardedRef === \"function\") {\n          forwardedRef(node);\n        } else if (forwardedRef) {\n          forwardedRef.current = node;\n        }\n      },\n      [forwardedRef]\n    );\n    const [isImgLoaded, setIsImageLoaded] = React.useState(false);\n    const { margin, cells, numCells, calculatedImageSettings } = useQRCode({\n      value,\n      level,\n      minVersion,\n      boostLevel,\n      includeMargin,\n      marginSize,\n      imageSettings,\n      size\n    });\n    React.useEffect(() => {\n      if (_canvas.current != null) {\n        const canvas = _canvas.current;\n        const ctx = canvas.getContext(\"2d\");\n        if (!ctx) {\n          return;\n        }\n        let cellsToDraw = cells;\n        const image = _image.current;\n        const haveImageToRender = calculatedImageSettings != null && image !== null && image.complete && image.naturalHeight !== 0 && image.naturalWidth !== 0;\n        if (haveImageToRender) {\n          if (calculatedImageSettings.excavation != null) {\n            cellsToDraw = excavateModules(\n              cells,\n              calculatedImageSettings.excavation\n            );\n          }\n        }\n        const pixelRatio = window.devicePixelRatio || 1;\n        canvas.height = canvas.width = size * pixelRatio;\n        const scale = size / numCells * pixelRatio;\n        ctx.scale(scale, scale);\n        ctx.fillStyle = bgColor;\n        ctx.fillRect(0, 0, numCells, numCells);\n        ctx.fillStyle = fgColor;\n        if (SUPPORTS_PATH2D) {\n          ctx.fill(new Path2D(generatePath(cellsToDraw, margin)));\n        } else {\n          cells.forEach(function(row, rdx) {\n            row.forEach(function(cell, cdx) {\n              if (cell) {\n                ctx.fillRect(cdx + margin, rdx + margin, 1, 1);\n              }\n            });\n          });\n        }\n        if (calculatedImageSettings) {\n          ctx.globalAlpha = calculatedImageSettings.opacity;\n        }\n        if (haveImageToRender) {\n          ctx.drawImage(\n            image,\n            calculatedImageSettings.x + margin,\n            calculatedImageSettings.y + margin,\n            calculatedImageSettings.w,\n            calculatedImageSettings.h\n          );\n        }\n      }\n    });\n    React.useEffect(() => {\n      setIsImageLoaded(false);\n    }, [imgSrc]);\n    const canvasStyle = __spreadValues({ height: size, width: size }, style);\n    let img = null;\n    if (imgSrc != null) {\n      img = /* @__PURE__ */ React.createElement(\n        \"img\",\n        {\n          src: imgSrc,\n          key: imgSrc,\n          style: { display: \"none\" },\n          onLoad: () => {\n            setIsImageLoaded(true);\n          },\n          ref: _image,\n          crossOrigin: calculatedImageSettings == null ? void 0 : calculatedImageSettings.crossOrigin\n        }\n      );\n    }\n    return /* @__PURE__ */ React.createElement(React.Fragment, null, /* @__PURE__ */ React.createElement(\n      \"canvas\",\n      __spreadValues({\n        style: canvasStyle,\n        height: size,\n        width: size,\n        ref: setCanvasRef,\n        role: \"img\"\n      }, otherProps)\n    ), img);\n  }\n);\nQRCodeCanvas.displayName = \"QRCodeCanvas\";\nvar QRCodeSVG = React.forwardRef(\n  function QRCodeSVG2(props, forwardedRef) {\n    const _a = props, {\n      value,\n      size = DEFAULT_SIZE,\n      level = DEFAULT_LEVEL,\n      bgColor = DEFAULT_BGCOLOR,\n      fgColor = DEFAULT_FGCOLOR,\n      includeMargin = DEFAULT_INCLUDEMARGIN,\n      minVersion = DEFAULT_MINVERSION,\n      boostLevel,\n      title,\n      marginSize,\n      imageSettings\n    } = _a, otherProps = __objRest(_a, [\n      \"value\",\n      \"size\",\n      \"level\",\n      \"bgColor\",\n      \"fgColor\",\n      \"includeMargin\",\n      \"minVersion\",\n      \"boostLevel\",\n      \"title\",\n      \"marginSize\",\n      \"imageSettings\"\n    ]);\n    const { margin, cells, numCells, calculatedImageSettings } = useQRCode({\n      value,\n      level,\n      minVersion,\n      boostLevel,\n      includeMargin,\n      marginSize,\n      imageSettings,\n      size\n    });\n    let cellsToDraw = cells;\n    let image = null;\n    if (imageSettings != null && calculatedImageSettings != null) {\n      if (calculatedImageSettings.excavation != null) {\n        cellsToDraw = excavateModules(\n          cells,\n          calculatedImageSettings.excavation\n        );\n      }\n      image = /* @__PURE__ */ React.createElement(\n        \"image\",\n        {\n          href: imageSettings.src,\n          height: calculatedImageSettings.h,\n          width: calculatedImageSettings.w,\n          x: calculatedImageSettings.x + margin,\n          y: calculatedImageSettings.y + margin,\n          preserveAspectRatio: \"none\",\n          opacity: calculatedImageSettings.opacity,\n          crossOrigin: calculatedImageSettings.crossOrigin\n        }\n      );\n    }\n    const fgPath = generatePath(cellsToDraw, margin);\n    return /* @__PURE__ */ React.createElement(\n      \"svg\",\n      __spreadValues({\n        height: size,\n        width: size,\n        viewBox: `0 0 ${numCells} ${numCells}`,\n        ref: forwardedRef,\n        role: \"img\"\n      }, otherProps),\n      !!title && /* @__PURE__ */ React.createElement(\"title\", null, title),\n      /* @__PURE__ */ React.createElement(\n        \"path\",\n        {\n          fill: bgColor,\n          d: `M0,0 h${numCells}v${numCells}H0z`,\n          shapeRendering: \"crispEdges\"\n        }\n      ),\n      /* @__PURE__ */ React.createElement(\"path\", { fill: fgColor, d: fgPath, shapeRendering: \"crispEdges\" }),\n      image\n    );\n  }\n);\nQRCodeSVG.displayName = \"QRCodeSVG\";\nexport {\n  QRCodeCanvas,\n  QRCodeSVG\n};\n"], "names": [], "mappings": ";;;;AA6BA,gBAAgB;AAChB;AA9BA,IAAI,YAAY,OAAO,cAAc;AACrC,IAAI,sBAAsB,OAAO,qBAAqB;AACtD,IAAI,eAAe,OAAO,SAAS,CAAC,cAAc;AAClD,IAAI,eAAe,OAAO,SAAS,CAAC,oBAAoB;AACxD,IAAI,kBAAkB,CAAC,KAAK,KAAK,QAAU,OAAO,MAAM,UAAU,KAAK,KAAK;QAAE,YAAY;QAAM,cAAc;QAAM,UAAU;QAAM;IAAM,KAAK,GAAG,CAAC,IAAI,GAAG;AAC1J,IAAI,iBAAiB,CAAC,GAAG;IACvB,IAAK,IAAI,QAAQ,KAAK,CAAC,IAAI,CAAC,CAAC,EAC3B,IAAI,aAAa,IAAI,CAAC,GAAG,OACvB,gBAAgB,GAAG,MAAM,CAAC,CAAC,KAAK;IACpC,IAAI,qBACF,KAAK,IAAI,QAAQ,oBAAoB,GAAI;QACvC,IAAI,aAAa,IAAI,CAAC,GAAG,OACvB,gBAAgB,GAAG,MAAM,CAAC,CAAC,KAAK;IACpC;IACF,OAAO;AACT;AACA,IAAI,YAAY,CAAC,QAAQ;IACvB,IAAI,SAAS,CAAC;IACd,IAAK,IAAI,QAAQ,OACf,IAAI,aAAa,IAAI,CAAC,QAAQ,SAAS,QAAQ,OAAO,CAAC,QAAQ,GAC7D,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK;IAC/B,IAAI,UAAU,QAAQ,qBACpB,KAAK,IAAI,QAAQ,oBAAoB,QAAS;QAC5C,IAAI,QAAQ,OAAO,CAAC,QAAQ,KAAK,aAAa,IAAI,CAAC,QAAQ,OACzD,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK;IAC/B;IACF,OAAO;AACT;;AAKA,qCAAqC;AACrC;;;;CAIC,GACD,IAAI;AACJ,CAAC,CAAC;IACA,MAAM,UAAU,MAAM;QACpB,0CAA0C,GAC1C,uDAAuD;QACvD,gEAAgE;QAChE,mEAAmE;QACnE,oDAAoD;QACpD,YAAY,OAAO,EAAE,oBAAoB,EAAE,aAAa,EAAE,GAAG,CAAE;YAC7D,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,oBAAoB,GAAG;YAC5B,4DAA4D;YAC5D,sEAAsE;YACtE,IAAI,CAAC,OAAO,GAAG,EAAE;YACjB,qGAAqG;YACrG,IAAI,CAAC,UAAU,GAAG,EAAE;YACpB,IAAI,UAAU,QAAQ,WAAW,IAAI,UAAU,QAAQ,WAAW,EAChE,MAAM,IAAI,WAAW;YACvB,IAAI,MAAM,CAAC,KAAK,MAAM,GACpB,MAAM,IAAI,WAAW;YACvB,IAAI,CAAC,IAAI,GAAG,UAAU,IAAI;YAC1B,IAAI,MAAM,EAAE;YACZ,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,IAC7B,IAAI,IAAI,CAAC;YACX,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,IAAK;gBAClC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,KAAK;gBAC3B,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,KAAK;YAChC;YACA,IAAI,CAAC,oBAAoB;YACzB,MAAM,eAAe,IAAI,CAAC,mBAAmB,CAAC;YAC9C,IAAI,CAAC,aAAa,CAAC;YACnB,IAAI,OAAO,CAAC,GAAG;gBACb,IAAI,aAAa;gBACjB,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;oBAC1B,IAAI,CAAC,SAAS,CAAC;oBACf,IAAI,CAAC,cAAc,CAAC;oBACpB,MAAM,UAAU,IAAI,CAAC,eAAe;oBACpC,IAAI,UAAU,YAAY;wBACxB,MAAM;wBACN,aAAa;oBACf;oBACA,IAAI,CAAC,SAAS,CAAC;gBACjB;YACF;YACA,OAAO,KAAK,OAAO,OAAO;YAC1B,IAAI,CAAC,IAAI,GAAG;YACZ,IAAI,CAAC,SAAS,CAAC;YACf,IAAI,CAAC,cAAc,CAAC;YACpB,IAAI,CAAC,UAAU,GAAG,EAAE;QACtB;QACA,6CAA6C,GAC7C,oGAAoG;QACpG,2GAA2G;QAC3G,+GAA+G;QAC/G,6GAA6G;QAC7G,iEAAiE;QACjE,OAAO,WAAW,IAAI,EAAE,GAAG,EAAE;YAC3B,MAAM,OAAO,WAAW,SAAS,CAAC,YAAY,CAAC;YAC/C,OAAO,QAAQ,cAAc,CAAC,MAAM;QACtC;QACA,4FAA4F;QAC5F,uGAAuG;QACvG,uGAAuG;QACvG,oHAAoH;QACpH,OAAO,aAAa,IAAI,EAAE,GAAG,EAAE;YAC7B,MAAM,MAAM,WAAW,SAAS,CAAC,SAAS,CAAC;YAC3C,OAAO,QAAQ,cAAc,CAAC;gBAAC;aAAI,EAAE;QACvC;QACA,4CAA4C,GAC5C,wFAAwF;QACxF,gFAAgF;QAChF,gFAAgF;QAChF,+EAA+E;QAC/E,8EAA8E;QAC9E,+EAA+E;QAC/E,sFAAsF;QACtF,8EAA8E;QAC9E,kFAAkF;QAClF,OAAO,eAAe,IAAI,EAAE,GAAG,EAAE,aAAa,CAAC,EAAE,aAAa,EAAE,EAAE,OAAO,CAAC,CAAC,EAAE,WAAW,IAAI,EAAE;YAC5F,IAAI,CAAC,CAAC,QAAQ,WAAW,IAAI,cAAc,cAAc,cAAc,cAAc,QAAQ,WAAW,KAAK,OAAO,CAAC,KAAK,OAAO,GAC/H,MAAM,IAAI,WAAW;YACvB,IAAI;YACJ,IAAI;YACJ,IAAK,UAAU,aAAc,UAAW;gBACtC,MAAM,oBAAoB,QAAQ,mBAAmB,CAAC,SAAS,OAAO;gBACtE,MAAM,WAAW,UAAU,YAAY,CAAC,MAAM;gBAC9C,IAAI,YAAY,mBAAmB;oBACjC,eAAe;oBACf;gBACF;gBACA,IAAI,WAAW,YACb,MAAM,IAAI,WAAW;YACzB;YACA,KAAK,MAAM,UAAU;gBAAC,QAAQ,GAAG,CAAC,MAAM;gBAAE,QAAQ,GAAG,CAAC,QAAQ;gBAAE,QAAQ,GAAG,CAAC,IAAI;aAAC,CAAE;gBACjF,IAAI,YAAY,gBAAgB,QAAQ,mBAAmB,CAAC,SAAS,UAAU,GAC7E,MAAM;YACV;YACA,IAAI,KAAK,EAAE;YACX,KAAK,MAAM,OAAO,KAAM;gBACtB,WAAW,IAAI,IAAI,CAAC,QAAQ,EAAE,GAAG;gBACjC,WAAW,IAAI,QAAQ,EAAE,IAAI,IAAI,CAAC,gBAAgB,CAAC,UAAU;gBAC7D,KAAK,MAAM,KAAK,IAAI,OAAO,GACzB,GAAG,IAAI,CAAC;YACZ;YACA,OAAO,GAAG,MAAM,IAAI;YACpB,MAAM,mBAAmB,QAAQ,mBAAmB,CAAC,SAAS,OAAO;YACrE,OAAO,GAAG,MAAM,IAAI;YACpB,WAAW,GAAG,KAAK,GAAG,CAAC,GAAG,mBAAmB,GAAG,MAAM,GAAG;YACzD,WAAW,GAAG,CAAC,IAAI,GAAG,MAAM,GAAG,CAAC,IAAI,GAAG;YACvC,OAAO,GAAG,MAAM,GAAG,KAAK;YACxB,IAAK,IAAI,UAAU,KAAK,GAAG,MAAM,GAAG,kBAAkB,WAAW,MAAM,GACrE,WAAW,SAAS,GAAG;YACzB,IAAI,gBAAgB,EAAE;YACtB,MAAO,cAAc,MAAM,GAAG,IAAI,GAAG,MAAM,CACzC,cAAc,IAAI,CAAC;YACrB,GAAG,OAAO,CAAC,CAAC,GAAG,IAAM,aAAa,CAAC,MAAM,EAAE,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC;YAC9D,OAAO,IAAI,QAAQ,SAAS,KAAK,eAAe;QAClD;QACA,wBAAwB,GACxB,mFAAmF;QACnF,kFAAkF;QAClF,8EAA8E;QAC9E,UAAU,CAAC,EAAE,CAAC,EAAE;YACd,OAAO,KAAK,KAAK,IAAI,IAAI,CAAC,IAAI,IAAI,KAAK,KAAK,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;QACjF;QACA,6CAA6C;QAC7C,aAAa;YACX,OAAO,IAAI,CAAC,OAAO;QACrB;QACA,wEAAwE,GACxE,+EAA+E;QAC/E,uBAAuB;YACrB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,IAAK;gBAClC,IAAI,CAAC,iBAAiB,CAAC,GAAG,GAAG,IAAI,KAAK;gBACtC,IAAI,CAAC,iBAAiB,CAAC,GAAG,GAAG,IAAI,KAAK;YACxC;YACA,IAAI,CAAC,iBAAiB,CAAC,GAAG;YAC1B,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,GAAG,GAAG;YACtC,IAAI,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC,IAAI,GAAG;YACtC,MAAM,cAAc,IAAI,CAAC,4BAA4B;YACrD,MAAM,WAAW,YAAY,MAAM;YACnC,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,IAAK;gBACjC,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,IAAK;oBACjC,IAAI,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,WAAW,KAAK,KAAK,WAAW,KAAK,KAAK,CAAC,GAClF,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,EAAE,EAAE,WAAW,CAAC,EAAE;gBAC5D;YACF;YACA,IAAI,CAAC,cAAc,CAAC;YACpB,IAAI,CAAC,WAAW;QAClB;QACA,2EAA2E;QAC3E,0EAA0E;QAC1E,eAAe,IAAI,EAAE;YACnB,MAAM,OAAO,IAAI,CAAC,oBAAoB,CAAC,UAAU,IAAI,IAAI;YACzD,IAAI,MAAM;YACV,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IACtB,MAAM,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI;YACjC,MAAM,OAAO,CAAC,QAAQ,KAAK,GAAG,IAAI;YAClC,OAAO,SAAS,MAAM;YACtB,IAAK,IAAI,IAAI,GAAG,KAAK,GAAG,IACtB,IAAI,CAAC,iBAAiB,CAAC,GAAG,GAAG,OAAO,MAAM;YAC5C,IAAI,CAAC,iBAAiB,CAAC,GAAG,GAAG,OAAO,MAAM;YAC1C,IAAI,CAAC,iBAAiB,CAAC,GAAG,GAAG,OAAO,MAAM;YAC1C,IAAI,CAAC,iBAAiB,CAAC,GAAG,GAAG,OAAO,MAAM;YAC1C,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IACtB,IAAI,CAAC,iBAAiB,CAAC,KAAK,GAAG,GAAG,OAAO,MAAM;YACjD,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IACrB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,GAAG,GAAG,OAAO,MAAM;YAC5D,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IACtB,IAAI,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC,IAAI,GAAG,KAAK,GAAG,OAAO,MAAM;YAC7D,IAAI,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC,IAAI,GAAG,GAAG;QAC3C;QACA,6EAA6E;QAC7E,gEAAgE;QAChE,cAAc;YACZ,IAAI,IAAI,CAAC,OAAO,GAAG,GACjB;YACF,IAAI,MAAM,IAAI,CAAC,OAAO;YACtB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IACtB,MAAM,OAAO,IAAI,CAAC,QAAQ,EAAE,IAAI;YAClC,MAAM,OAAO,IAAI,CAAC,OAAO,IAAI,KAAK;YAClC,OAAO,SAAS,MAAM;YACtB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAK;gBAC3B,MAAM,QAAQ,OAAO,MAAM;gBAC3B,MAAM,IAAI,IAAI,CAAC,IAAI,GAAG,KAAK,IAAI;gBAC/B,MAAM,IAAI,KAAK,KAAK,CAAC,IAAI;gBACzB,IAAI,CAAC,iBAAiB,CAAC,GAAG,GAAG;gBAC7B,IAAI,CAAC,iBAAiB,CAAC,GAAG,GAAG;YAC/B;QACF;QACA,6DAA6D;QAC7D,kEAAkE;QAClE,kBAAkB,CAAC,EAAE,CAAC,EAAE;YACtB,IAAK,IAAI,KAAK,CAAC,GAAG,MAAM,GAAG,KAAM;gBAC/B,IAAK,IAAI,KAAK,CAAC,GAAG,MAAM,GAAG,KAAM;oBAC/B,MAAM,OAAO,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC;oBAC7C,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;oBACf,IAAI,KAAK,MAAM,KAAK,IAAI,CAAC,IAAI,IAAI,KAAK,MAAM,KAAK,IAAI,CAAC,IAAI,EACxD,IAAI,CAAC,iBAAiB,CAAC,IAAI,IAAI,QAAQ,KAAK,QAAQ;gBACxD;YACF;QACF;QACA,wDAAwD;QACxD,4CAA4C;QAC5C,qBAAqB,CAAC,EAAE,CAAC,EAAE;YACzB,IAAK,IAAI,KAAK,CAAC,GAAG,MAAM,GAAG,KAAM;gBAC/B,IAAK,IAAI,KAAK,CAAC,GAAG,MAAM,GAAG,KACzB,IAAI,CAAC,iBAAiB,CAAC,IAAI,IAAI,IAAI,IAAI,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,QAAQ;YACnF;QACF;QACA,gEAAgE;QAChE,+DAA+D;QAC/D,kBAAkB,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE;YAC9B,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,GAAG;YACrB,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE,GAAG;QAC1B;QACA,qEAAqE,GACrE,8FAA8F;QAC9F,uFAAuF;QACvF,oBAAoB,IAAI,EAAE;YACxB,MAAM,MAAM,IAAI,CAAC,OAAO;YACxB,MAAM,MAAM,IAAI,CAAC,oBAAoB;YACrC,IAAI,KAAK,MAAM,IAAI,QAAQ,mBAAmB,CAAC,KAAK,MAClD,MAAM,IAAI,WAAW;YACvB,MAAM,YAAY,QAAQ,2BAA2B,CAAC,IAAI,OAAO,CAAC,CAAC,IAAI;YACvE,MAAM,cAAc,QAAQ,uBAAuB,CAAC,IAAI,OAAO,CAAC,CAAC,IAAI;YACrE,MAAM,eAAe,KAAK,KAAK,CAAC,QAAQ,oBAAoB,CAAC,OAAO;YACpE,MAAM,iBAAiB,YAAY,eAAe;YAClD,MAAM,gBAAgB,KAAK,KAAK,CAAC,eAAe;YAChD,IAAI,SAAS,EAAE;YACf,MAAM,QAAQ,QAAQ,yBAAyB,CAAC;YAChD,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,WAAW,IAAK;gBACzC,IAAI,MAAM,KAAK,KAAK,CAAC,GAAG,IAAI,gBAAgB,cAAc,CAAC,IAAI,iBAAiB,IAAI,CAAC;gBACrF,KAAK,IAAI,MAAM;gBACf,MAAM,MAAM,QAAQ,2BAA2B,CAAC,KAAK;gBACrD,IAAI,IAAI,gBACN,IAAI,IAAI,CAAC;gBACX,OAAO,IAAI,CAAC,IAAI,MAAM,CAAC;YACzB;YACA,IAAI,SAAS,EAAE;YACf,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,IAAK;gBACzC,OAAO,OAAO,CAAC,CAAC,OAAO;oBACrB,IAAI,KAAK,gBAAgB,eAAe,KAAK,gBAC3C,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE;gBACxB;YACF;YACA,OAAO,OAAO,MAAM,IAAI;YACxB,OAAO;QACT;QACA,0FAA0F;QAC1F,2FAA2F;QAC3F,cAAc,IAAI,EAAE;YAClB,IAAI,KAAK,MAAM,IAAI,KAAK,KAAK,CAAC,QAAQ,oBAAoB,CAAC,IAAI,CAAC,OAAO,IAAI,IACzE,MAAM,IAAI,WAAW;YACvB,IAAI,IAAI;YACR,IAAK,IAAI,QAAQ,IAAI,CAAC,IAAI,GAAG,GAAG,SAAS,GAAG,SAAS,EAAG;gBACtD,IAAI,SAAS,GACX,QAAQ;gBACV,IAAK,IAAI,OAAO,GAAG,OAAO,IAAI,CAAC,IAAI,EAAE,OAAQ;oBAC3C,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;wBAC1B,MAAM,IAAI,QAAQ;wBAClB,MAAM,SAAS,CAAC,QAAQ,IAAI,CAAC,KAAK;wBAClC,MAAM,IAAI,SAAS,IAAI,CAAC,IAAI,GAAG,IAAI,OAAO;wBAC1C,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,IAAI,KAAK,MAAM,GAAG,GAAG;4BACjD,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,GAAG,OAAO,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC;4BACrD;wBACF;oBACF;gBACF;YACF;YACA,OAAO,KAAK,KAAK,MAAM,GAAG;QAC5B;QACA,yEAAyE;QACzE,0EAA0E;QAC1E,yEAAyE;QACzE,4EAA4E;QAC5E,gEAAgE;QAChE,UAAU,IAAI,EAAE;YACd,IAAI,OAAO,KAAK,OAAO,GACrB,MAAM,IAAI,WAAW;YACvB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,IAAK;gBAClC,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,IAAK;oBAClC,IAAI;oBACJ,OAAQ;wBACN,KAAK;4BACH,SAAS,CAAC,IAAI,CAAC,IAAI,KAAK;4BACxB;wBACF,KAAK;4BACH,SAAS,IAAI,KAAK;4BAClB;wBACF,KAAK;4BACH,SAAS,IAAI,KAAK;4BAClB;wBACF,KAAK;4BACH,SAAS,CAAC,IAAI,CAAC,IAAI,KAAK;4BACxB;wBACF,KAAK;4BACH,SAAS,CAAC,KAAK,KAAK,CAAC,IAAI,KAAK,KAAK,KAAK,CAAC,IAAI,EAAE,IAAI,KAAK;4BACxD;wBACF,KAAK;4BACH,SAAS,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK;4BAClC;wBACF,KAAK;4BACH,SAAS,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK;4BACxC;wBACF,KAAK;4BACH,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK;4BAC1C;wBACF;4BACE,MAAM,IAAI,MAAM;oBACpB;oBACA,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,QAC5B,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;gBAC5C;YACF;QACF;QACA,6FAA6F;QAC7F,6GAA6G;QAC7G,kBAAkB;YAChB,IAAI,SAAS;YACb,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,IAAK;gBAClC,IAAI,WAAW;gBACf,IAAI,OAAO;gBACX,IAAI,aAAa;oBAAC;oBAAG;oBAAG;oBAAG;oBAAG;oBAAG;oBAAG;iBAAE;gBACtC,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,IAAK;oBAClC,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,IAAI,UAAU;wBAClC;wBACA,IAAI,QAAQ,GACV,UAAU,QAAQ,UAAU;6BACzB,IAAI,OAAO,GACd;oBACJ,OAAO;wBACL,IAAI,CAAC,uBAAuB,CAAC,MAAM;wBACnC,IAAI,CAAC,UACH,UAAU,IAAI,CAAC,0BAA0B,CAAC,cAAc,QAAQ,UAAU;wBAC5E,WAAW,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;wBAC7B,OAAO;oBACT;gBACF;gBACA,UAAU,IAAI,CAAC,8BAA8B,CAAC,UAAU,MAAM,cAAc,QAAQ,UAAU;YAChG;YACA,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,IAAK;gBAClC,IAAI,WAAW;gBACf,IAAI,OAAO;gBACX,IAAI,aAAa;oBAAC;oBAAG;oBAAG;oBAAG;oBAAG;oBAAG;oBAAG;iBAAE;gBACtC,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,IAAK;oBAClC,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,IAAI,UAAU;wBAClC;wBACA,IAAI,QAAQ,GACV,UAAU,QAAQ,UAAU;6BACzB,IAAI,OAAO,GACd;oBACJ,OAAO;wBACL,IAAI,CAAC,uBAAuB,CAAC,MAAM;wBACnC,IAAI,CAAC,UACH,UAAU,IAAI,CAAC,0BAA0B,CAAC,cAAc,QAAQ,UAAU;wBAC5E,WAAW,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;wBAC7B,OAAO;oBACT;gBACF;gBACA,UAAU,IAAI,CAAC,8BAA8B,CAAC,UAAU,MAAM,cAAc,QAAQ,UAAU;YAChG;YACA,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,GAAG,GAAG,IAAK;gBACtC,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,GAAG,GAAG,IAAK;oBACtC,MAAM,QAAQ,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;oBAChC,IAAI,SAAS,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,SAAS,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,SAAS,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,EAC3G,UAAU,QAAQ,UAAU;gBAChC;YACF;YACA,IAAI,OAAO;YACX,KAAK,MAAM,OAAO,IAAI,CAAC,OAAO,CAC5B,OAAO,IAAI,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,CAAC,QAAQ,IAAI,CAAC,GAAG;YAC3D,MAAM,QAAQ,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI;YACnC,MAAM,IAAI,KAAK,IAAI,CAAC,KAAK,GAAG,CAAC,OAAO,KAAK,QAAQ,MAAM,SAAS;YAChE,OAAO,KAAK,KAAK,KAAK;YACtB,UAAU,IAAI,QAAQ,UAAU;YAChC,OAAO,KAAK,UAAU,UAAU;YAChC,OAAO;QACT;QACA,gCAAgC,GAChC,wFAAwF;QACxF,gFAAgF;QAChF,qFAAqF;QACrF,+BAA+B;YAC7B,IAAI,IAAI,CAAC,OAAO,IAAI,GAClB,OAAO,EAAE;iBACN;gBACH,MAAM,WAAW,KAAK,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG,KAAK;gBAChD,MAAM,OAAO,IAAI,CAAC,OAAO,IAAI,KAAK,KAAK,KAAK,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,KAAK;gBAChG,IAAI,SAAS;oBAAC;iBAAE;gBAChB,IAAK,IAAI,MAAM,IAAI,CAAC,IAAI,GAAG,GAAG,OAAO,MAAM,GAAG,UAAU,OAAO,KAC7D,OAAO,MAAM,CAAC,GAAG,GAAG;gBACtB,OAAO;YACT;QACF;QACA,qGAAqG;QACrG,uGAAuG;QACvG,iGAAiG;QACjG,OAAO,qBAAqB,GAAG,EAAE;YAC/B,IAAI,MAAM,QAAQ,WAAW,IAAI,MAAM,QAAQ,WAAW,EACxD,MAAM,IAAI,WAAW;YACvB,IAAI,SAAS,CAAC,KAAK,MAAM,GAAG,IAAI,MAAM;YACtC,IAAI,OAAO,GAAG;gBACZ,MAAM,WAAW,KAAK,KAAK,CAAC,MAAM,KAAK;gBACvC,UAAU,CAAC,KAAK,WAAW,EAAE,IAAI,WAAW;gBAC5C,IAAI,OAAO,GACT,UAAU;YACd;YACA,OAAO,OAAO,UAAU,UAAU;YAClC,OAAO;QACT;QACA,0FAA0F;QAC1F,iGAAiG;QACjG,mFAAmF;QACnF,OAAO,oBAAoB,GAAG,EAAE,GAAG,EAAE;YACnC,OAAO,KAAK,KAAK,CAAC,QAAQ,oBAAoB,CAAC,OAAO,KAAK,QAAQ,uBAAuB,CAAC,IAAI,OAAO,CAAC,CAAC,IAAI,GAAG,QAAQ,2BAA2B,CAAC,IAAI,OAAO,CAAC,CAAC,IAAI;QACtK;QACA,sFAAsF;QACtF,gGAAgG;QAChG,OAAO,0BAA0B,MAAM,EAAE;YACvC,IAAI,SAAS,KAAK,SAAS,KACzB,MAAM,IAAI,WAAW;YACvB,IAAI,SAAS,EAAE;YACf,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,GAAG,IAC9B,OAAO,IAAI,CAAC;YACd,OAAO,IAAI,CAAC;YACZ,IAAI,OAAO;YACX,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;gBAC/B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;oBACtC,MAAM,CAAC,EAAE,GAAG,QAAQ,mBAAmB,CAAC,MAAM,CAAC,EAAE,EAAE;oBACnD,IAAI,IAAI,IAAI,OAAO,MAAM,EACvB,MAAM,CAAC,EAAE,IAAI,MAAM,CAAC,IAAI,EAAE;gBAC9B;gBACA,OAAO,QAAQ,mBAAmB,CAAC,MAAM;YAC3C;YACA,OAAO;QACT;QACA,iGAAiG;QACjG,OAAO,4BAA4B,IAAI,EAAE,OAAO,EAAE;YAChD,IAAI,SAAS,QAAQ,GAAG,CAAC,CAAC,IAAM;YAChC,KAAK,MAAM,KAAK,KAAM;gBACpB,MAAM,SAAS,IAAI,OAAO,KAAK;gBAC/B,OAAO,IAAI,CAAC;gBACZ,QAAQ,OAAO,CAAC,CAAC,MAAM,IAAM,MAAM,CAAC,EAAE,IAAI,QAAQ,mBAAmB,CAAC,MAAM;YAC9E;YACA,OAAO;QACT;QACA,qGAAqG;QACrG,wGAAwG;QACxG,OAAO,oBAAoB,CAAC,EAAE,CAAC,EAAE;YAC/B,IAAI,MAAM,KAAK,KAAK,MAAM,KAAK,GAC7B,MAAM,IAAI,WAAW;YACvB,IAAI,IAAI;YACR,IAAK,IAAI,IAAI,GAAG,KAAK,GAAG,IAAK;gBAC3B,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,IAAI;gBACzB,KAAK,CAAC,MAAM,IAAI,CAAC,IAAI;YACvB;YACA,OAAO,MAAM,KAAK;YAClB,OAAO;QACT;QACA,iEAAiE;QACjE,sEAAsE;QACtE,2BAA2B,UAAU,EAAE;YACrC,MAAM,IAAI,UAAU,CAAC,EAAE;YACvB,OAAO,KAAK,IAAI,CAAC,IAAI,GAAG;YACxB,MAAM,OAAO,IAAI,KAAK,UAAU,CAAC,EAAE,IAAI,KAAK,UAAU,CAAC,EAAE,IAAI,IAAI,KAAK,UAAU,CAAC,EAAE,IAAI,KAAK,UAAU,CAAC,EAAE,IAAI;YAC7G,OAAO,CAAC,QAAQ,UAAU,CAAC,EAAE,IAAI,IAAI,KAAK,UAAU,CAAC,EAAE,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,UAAU,CAAC,EAAE,IAAI,IAAI,KAAK,UAAU,CAAC,EAAE,IAAI,IAAI,IAAI,CAAC;QACvI;QACA,2GAA2G;QAC3G,+BAA+B,eAAe,EAAE,gBAAgB,EAAE,UAAU,EAAE;YAC5E,IAAI,iBAAiB;gBACnB,IAAI,CAAC,uBAAuB,CAAC,kBAAkB;gBAC/C,mBAAmB;YACrB;YACA,oBAAoB,IAAI,CAAC,IAAI;YAC7B,IAAI,CAAC,uBAAuB,CAAC,kBAAkB;YAC/C,OAAO,IAAI,CAAC,0BAA0B,CAAC;QACzC;QACA,yGAAyG;QACzG,wBAAwB,gBAAgB,EAAE,UAAU,EAAE;YACpD,IAAI,UAAU,CAAC,EAAE,IAAI,GACnB,oBAAoB,IAAI,CAAC,IAAI;YAC/B,WAAW,GAAG;YACd,WAAW,OAAO,CAAC;QACrB;IACF;IACA,4BAA4B,GAC5B,wEAAwE;IACxE,QAAQ,WAAW,GAAG;IACtB,wEAAwE;IACxE,QAAQ,WAAW,GAAG;IACtB,oEAAoE;IACpE,QAAQ,UAAU,GAAG;IACrB,QAAQ,UAAU,GAAG;IACrB,QAAQ,UAAU,GAAG;IACrB,QAAQ,UAAU,GAAG;IACrB,QAAQ,uBAAuB,GAAG;QAChC,8EAA8E;QAC9E,6LAA6L;QAC7L;YAAC,CAAC;YAAG;YAAG;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;SAAG;QACnK,MAAM;QACN;YAAC,CAAC;YAAG;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;SAAG;QACpK,SAAS;QACT;YAAC,CAAC;YAAG;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;SAAG;QACpK,WAAW;QACX;YAAC,CAAC;YAAG;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;SAAG;KAErK;IACD,QAAQ,2BAA2B,GAAG;QACpC,8EAA8E;QAC9E,mLAAmL;QACnL;YAAC,CAAC;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;SAAG;QAC7I,MAAM;QACN;YAAC,CAAC;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;SAAG;QACtJ,SAAS;QACT;YAAC,CAAC;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;SAAG;QACzJ,WAAW;QACX;YAAC,CAAC;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;SAAG;KAE3J;IACD,IAAI,SAAS;IACb,WAAW,MAAM,GAAG;IACpB,SAAS,WAAW,GAAG,EAAE,GAAG,EAAE,EAAE;QAC9B,IAAI,MAAM,KAAK,MAAM,MAAM,QAAQ,OAAO,GACxC,MAAM,IAAI,WAAW;QACvB,IAAK,IAAI,IAAI,MAAM,GAAG,KAAK,GAAG,IAC5B,GAAG,IAAI,CAAC,QAAQ,IAAI;IACxB;IACA,SAAS,OAAO,CAAC,EAAE,CAAC;QAClB,OAAO,CAAC,MAAM,IAAI,CAAC,KAAK;IAC1B;IACA,SAAS,OAAO,IAAI;QAClB,IAAI,CAAC,MACH,MAAM,IAAI,MAAM;IACpB;IACA,MAAM,aAAa,MAAM;QACvB,0CAA0C,GAC1C,oEAAoE;QACpE,qFAAqF;QACrF,+EAA+E;QAC/E,YAAY,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAE;YACnC,IAAI,CAAC,IAAI,GAAG;YACZ,IAAI,CAAC,QAAQ,GAAG;YAChB,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,WAAW,GACb,MAAM,IAAI,WAAW;YACvB,IAAI,CAAC,OAAO,GAAG,QAAQ,KAAK;QAC9B;QACA,4CAA4C,GAC5C,kEAAkE;QAClE,mEAAmE;QACnE,sEAAsE;QACtE,OAAO,UAAU,IAAI,EAAE;YACrB,IAAI,KAAK,EAAE;YACX,KAAK,MAAM,KAAK,KACd,WAAW,GAAG,GAAG;YACnB,OAAO,IAAI,WAAW,WAAW,IAAI,CAAC,IAAI,EAAE,KAAK,MAAM,EAAE;QAC3D;QACA,6FAA6F;QAC7F,OAAO,YAAY,MAAM,EAAE;YACzB,IAAI,CAAC,WAAW,SAAS,CAAC,SACxB,MAAM,IAAI,WAAW;YACvB,IAAI,KAAK,EAAE;YACX,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAI;gBACnC,MAAM,IAAI,KAAK,GAAG,CAAC,OAAO,MAAM,GAAG,GAAG;gBACtC,WAAW,SAAS,OAAO,SAAS,CAAC,GAAG,IAAI,IAAI,KAAK,IAAI,IAAI,GAAG;gBAChE,KAAK;YACP;YACA,OAAO,IAAI,WAAW,WAAW,IAAI,CAAC,OAAO,EAAE,OAAO,MAAM,EAAE;QAChE;QACA,qFAAqF;QACrF,sEAAsE;QACtE,iEAAiE;QACjE,OAAO,iBAAiB,IAAI,EAAE;YAC5B,IAAI,CAAC,WAAW,cAAc,CAAC,OAC7B,MAAM,IAAI,WAAW;YACvB,IAAI,KAAK,EAAE;YACX,IAAI;YACJ,IAAK,IAAI,GAAG,IAAI,KAAK,KAAK,MAAM,EAAE,KAAK,EAAG;gBACxC,IAAI,OAAO,WAAW,oBAAoB,CAAC,OAAO,CAAC,KAAK,MAAM,CAAC,MAAM;gBACrE,QAAQ,WAAW,oBAAoB,CAAC,OAAO,CAAC,KAAK,MAAM,CAAC,IAAI;gBAChE,WAAW,MAAM,IAAI;YACvB;YACA,IAAI,IAAI,KAAK,MAAM,EACjB,WAAW,WAAW,oBAAoB,CAAC,OAAO,CAAC,KAAK,MAAM,CAAC,KAAK,GAAG;YACzE,OAAO,IAAI,WAAW,WAAW,IAAI,CAAC,YAAY,EAAE,KAAK,MAAM,EAAE;QACnE;QACA,kGAAkG;QAClG,sGAAsG;QACtG,OAAO,aAAa,IAAI,EAAE;YACxB,IAAI,QAAQ,IACV,OAAO,EAAE;iBACN,IAAI,WAAW,SAAS,CAAC,OAC5B,OAAO;gBAAC,WAAW,WAAW,CAAC;aAAM;iBAClC,IAAI,WAAW,cAAc,CAAC,OACjC,OAAO;gBAAC,WAAW,gBAAgB,CAAC;aAAM;iBAE1C,OAAO;gBAAC,WAAW,SAAS,CAAC,WAAW,eAAe,CAAC;aAAO;QACnE;QACA,oEAAoE;QACpE,oDAAoD;QACpD,OAAO,QAAQ,SAAS,EAAE;YACxB,IAAI,KAAK,EAAE;YACX,IAAI,YAAY,GACd,MAAM,IAAI,WAAW;iBAClB,IAAI,YAAY,KAAK,GACxB,WAAW,WAAW,GAAG;iBACtB,IAAI,YAAY,KAAK,IAAI;gBAC5B,WAAW,GAAG,GAAG;gBACjB,WAAW,WAAW,IAAI;YAC5B,OAAO,IAAI,YAAY,KAAK;gBAC1B,WAAW,GAAG,GAAG;gBACjB,WAAW,WAAW,IAAI;YAC5B,OACE,MAAM,IAAI,WAAW;YACvB,OAAO,IAAI,WAAW,WAAW,IAAI,CAAC,GAAG,EAAE,GAAG;QAChD;QACA,8EAA8E;QAC9E,mEAAmE;QACnE,OAAO,UAAU,IAAI,EAAE;YACrB,OAAO,WAAW,aAAa,CAAC,IAAI,CAAC;QACvC;QACA,mFAAmF;QACnF,mFAAmF;QACnF,0FAA0F;QAC1F,OAAO,eAAe,IAAI,EAAE;YAC1B,OAAO,WAAW,kBAAkB,CAAC,IAAI,CAAC;QAC5C;QACA,eAAe,GACf,uDAAuD;QACvD,UAAU;YACR,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK;QAC3B;QACA,qGAAqG;QACrG,0GAA0G;QAC1G,OAAO,aAAa,IAAI,EAAE,OAAO,EAAE;YACjC,IAAI,SAAS;YACb,KAAK,MAAM,OAAO,KAAM;gBACtB,MAAM,SAAS,IAAI,IAAI,CAAC,gBAAgB,CAAC;gBACzC,IAAI,IAAI,QAAQ,IAAI,KAAK,QACvB,OAAO;gBACT,UAAU,IAAI,SAAS,IAAI,OAAO,CAAC,MAAM;YAC3C;YACA,OAAO;QACT;QACA,+EAA+E;QAC/E,OAAO,gBAAgB,GAAG,EAAE;YAC1B,MAAM,UAAU;YAChB,IAAI,SAAS,EAAE;YACf,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;gBACnC,IAAI,IAAI,MAAM,CAAC,MAAM,KACnB,OAAO,IAAI,CAAC,IAAI,UAAU,CAAC;qBACxB;oBACH,OAAO,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,IAAI,GAAG,IAAI,IAAI;oBAClD,KAAK;gBACP;YACF;YACA,OAAO;QACT;IACF;IACA,iBAAiB,GACjB,sEAAsE;IACtE,WAAW,aAAa,GAAG;IAC3B,2EAA2E;IAC3E,WAAW,kBAAkB,GAAG;IAChC,wDAAwD;IACxD,8DAA8D;IAC9D,WAAW,oBAAoB,GAAG;IAClC,IAAI,YAAY;IAChB,WAAW,SAAS,GAAG;AACzB,CAAC,EAAE,aAAa,CAAC,YAAY,CAAC,CAAC;AAC/B,CAAC,CAAC;IACA,IAAI;IACJ,CAAC,CAAC;QACA,MAAM,OAAO,MAAM;YACjB,yDAAyD;YACzD,8BAA8B,GAC9B,YAAY,OAAO,EAAE,UAAU,CAAE;gBAC/B,IAAI,CAAC,OAAO,GAAG;gBACf,IAAI,CAAC,UAAU,GAAG;YACpB;QACF;QACA,iBAAiB,GACjB,KAAK,GAAG,GAAG,IAAI,KAAK,GAAG;QACvB,yDAAyD;QACzD,KAAK,MAAM,GAAG,IAAI,KAAK,GAAG;QAC1B,yDAAyD;QACzD,KAAK,QAAQ,GAAG,IAAI,KAAK,GAAG;QAC5B,yDAAyD;QACzD,KAAK,IAAI,GAAG,IAAI,KAAK,GAAG;QACxB,IAAI,MAAM;QACV,QAAQ,GAAG,GAAG;IAChB,CAAC,EAAE,SAAS,WAAW,MAAM,IAAI,CAAC,WAAW,MAAM,GAAG,CAAC,CAAC;AAC1D,CAAC,EAAE,aAAa,CAAC,YAAY,CAAC,CAAC;AAC/B,CAAC,CAAC;IACA,IAAI;IACJ,CAAC,CAAC;QACA,MAAM,QAAQ,MAAM;YAClB,8BAA8B,GAC9B,YAAY,QAAQ,EAAE,gBAAgB,CAAE;gBACtC,IAAI,CAAC,QAAQ,GAAG;gBAChB,IAAI,CAAC,gBAAgB,GAAG;YAC1B;YACA,cAAc,GACd,wFAAwF;YACxF,0FAA0F;YAC1F,iBAAiB,GAAG,EAAE;gBACpB,OAAO,IAAI,CAAC,gBAAgB,CAAC,KAAK,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,IAAI;YAC1D;QACF;QACA,iBAAiB,GACjB,MAAM,OAAO,GAAG,IAAI,MAAM,GAAG;YAAC;YAAI;YAAI;SAAG;QACzC,MAAM,YAAY,GAAG,IAAI,MAAM,GAAG;YAAC;YAAG;YAAI;SAAG;QAC7C,MAAM,IAAI,GAAG,IAAI,MAAM,GAAG;YAAC;YAAG;YAAI;SAAG;QACrC,MAAM,KAAK,GAAG,IAAI,MAAM,GAAG;YAAC;YAAG;YAAI;SAAG;QACtC,MAAM,GAAG,GAAG,IAAI,MAAM,GAAG;YAAC;YAAG;YAAG;SAAE;QAClC,IAAI,OAAO;QACX,WAAW,IAAI,GAAG;IACpB,CAAC,EAAE,YAAY,WAAW,SAAS,IAAI,CAAC,WAAW,SAAS,GAAG,CAAC,CAAC;AACnE,CAAC,EAAE,aAAa,CAAC,YAAY,CAAC,CAAC;AAC/B,IAAI,oBAAoB;AAExB,gBAAgB;AAChB;;;;CAIC,GACD,IAAI,kBAAkB;IACpB,GAAG,kBAAkB,MAAM,CAAC,GAAG,CAAC,GAAG;IACnC,GAAG,kBAAkB,MAAM,CAAC,GAAG,CAAC,MAAM;IACtC,GAAG,kBAAkB,MAAM,CAAC,GAAG,CAAC,QAAQ;IACxC,GAAG,kBAAkB,MAAM,CAAC,GAAG,CAAC,IAAI;AACtC;AACA,IAAI,eAAe;AACnB,IAAI,gBAAgB;AACpB,IAAI,kBAAkB;AACtB,IAAI,kBAAkB;AACtB,IAAI,wBAAwB;AAC5B,IAAI,qBAAqB;AACzB,IAAI,mBAAmB;AACvB,IAAI,sBAAsB;AAC1B,IAAI,oBAAoB;AACxB,SAAS,aAAa,OAAO,EAAE,SAAS,CAAC;IACvC,MAAM,MAAM,EAAE;IACd,QAAQ,OAAO,CAAC,SAAS,GAAG,EAAE,CAAC;QAC7B,IAAI,QAAQ;QACZ,IAAI,OAAO,CAAC,SAAS,IAAI,EAAE,CAAC;YAC1B,IAAI,CAAC,QAAQ,UAAU,MAAM;gBAC3B,IAAI,IAAI,CACN,CAAC,CAAC,EAAE,QAAQ,OAAO,CAAC,EAAE,IAAI,OAAO,CAAC,EAAE,IAAI,MAAM,GAAG,EAAE,QAAQ,OAAO,CAAC,CAAC;gBAEtE,QAAQ;gBACR;YACF;YACA,IAAI,MAAM,IAAI,MAAM,GAAG,GAAG;gBACxB,IAAI,CAAC,MAAM;oBACT;gBACF;gBACA,IAAI,UAAU,MAAM;oBAClB,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,OAAO,CAAC,EAAE,IAAI,OAAO,MAAM,EAAE,IAAI,OAAO,CAAC,CAAC;gBAC7D,OAAO;oBACL,IAAI,IAAI,CACN,CAAC,CAAC,EAAE,QAAQ,OAAO,CAAC,EAAE,IAAI,OAAO,EAAE,EAAE,IAAI,IAAI,MAAM,GAAG,EAAE,QAAQ,OAAO,CAAC,CAAC;gBAE7E;gBACA;YACF;YACA,IAAI,QAAQ,UAAU,MAAM;gBAC1B,QAAQ;YACV;QACF;IACF;IACA,OAAO,IAAI,IAAI,CAAC;AAClB;AACA,SAAS,gBAAgB,OAAO,EAAE,UAAU;IAC1C,OAAO,QAAQ,KAAK,GAAG,GAAG,CAAC,CAAC,KAAK;QAC/B,IAAI,IAAI,WAAW,CAAC,IAAI,KAAK,WAAW,CAAC,GAAG,WAAW,CAAC,EAAE;YACxD,OAAO;QACT;QACA,OAAO,IAAI,GAAG,CAAC,CAAC,MAAM;YACpB,IAAI,IAAI,WAAW,CAAC,IAAI,KAAK,WAAW,CAAC,GAAG,WAAW,CAAC,EAAE;gBACxD,OAAO;YACT;YACA,OAAO;QACT;IACF;AACF;AACA,SAAS,iBAAiB,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,aAAa;IAC1D,IAAI,iBAAiB,MAAM;QACzB,OAAO;IACT;IACA,MAAM,WAAW,MAAM,MAAM,GAAG,SAAS;IACzC,MAAM,cAAc,KAAK,KAAK,CAAC,OAAO;IACtC,MAAM,QAAQ,WAAW;IACzB,MAAM,IAAI,CAAC,cAAc,KAAK,IAAI,WAAW,IAAI;IACjD,MAAM,IAAI,CAAC,cAAc,MAAM,IAAI,WAAW,IAAI;IAClD,MAAM,IAAI,cAAc,CAAC,IAAI,OAAO,MAAM,MAAM,GAAG,IAAI,IAAI,IAAI,cAAc,CAAC,GAAG;IACjF,MAAM,IAAI,cAAc,CAAC,IAAI,OAAO,MAAM,MAAM,GAAG,IAAI,IAAI,IAAI,cAAc,CAAC,GAAG;IACjF,MAAM,UAAU,cAAc,OAAO,IAAI,OAAO,IAAI,cAAc,OAAO;IACzE,IAAI,aAAa;IACjB,IAAI,cAAc,QAAQ,EAAE;QAC1B,IAAI,SAAS,KAAK,KAAK,CAAC;QACxB,IAAI,SAAS,KAAK,KAAK,CAAC;QACxB,IAAI,QAAQ,KAAK,IAAI,CAAC,IAAI,IAAI;QAC9B,IAAI,QAAQ,KAAK,IAAI,CAAC,IAAI,IAAI;QAC9B,aAAa;YAAE,GAAG;YAAQ,GAAG;YAAQ,GAAG;YAAO,GAAG;QAAM;IAC1D;IACA,MAAM,cAAc,cAAc,WAAW;IAC7C,OAAO;QAAE;QAAG;QAAG;QAAG;QAAG;QAAY;QAAS;IAAY;AACxD;AACA,SAAS,cAAc,aAAa,EAAE,UAAU;IAC9C,IAAI,cAAc,MAAM;QACtB,OAAO,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,aAAa;IAC1C;IACA,OAAO,gBAAgB,mBAAmB;AAC5C;AACA,SAAS,UAAU,EACjB,KAAK,EACL,KAAK,EACL,UAAU,EACV,aAAa,EACb,UAAU,EACV,aAAa,EACb,IAAI,EACJ,UAAU,EACX;IACC,IAAI,SAAS,6JAAA,CAAA,UAAK,CAAC,OAAO;qCAAC;YACzB,MAAM,SAAS,MAAM,OAAO,CAAC,SAAS,QAAQ;gBAAC;aAAM;YACrD,MAAM,WAAW,OAAO,MAAM;sDAAC,CAAC,OAAO;oBACrC,MAAM,IAAI,IAAI,kBAAkB,SAAS,CAAC,YAAY,CAAC;oBACvD,OAAO;gBACT;qDAAG,EAAE;YACL,OAAO,kBAAkB,MAAM,CAAC,cAAc,CAC5C,UACA,eAAe,CAAC,MAAM,EACtB,YACA,KAAK,GACL,KAAK,GACL;QAEJ;oCAAG;QAAC;QAAO;QAAO;QAAY;KAAW;IACzC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,uBAAuB,EAAE,GAAG,6JAAA,CAAA,UAAK,CAAC,OAAO;6BAAC;YACzE,IAAI,SAAS,OAAO,UAAU;YAC9B,MAAM,UAAU,cAAc,eAAe;YAC7C,MAAM,YAAY,OAAO,MAAM,GAAG,UAAU;YAC5C,MAAM,2BAA2B,iBAC/B,QACA,MACA,SACA;YAEF,OAAO;gBACL,OAAO;gBACP,QAAQ;gBACR,UAAU;gBACV,yBAAyB;YAC3B;QACF;4BAAG;QAAC;QAAQ;QAAM;QAAe;QAAe;KAAW;IAC3D,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;AACA,IAAI,kBAAkB;IACpB,IAAI;QACF,IAAI,SAAS,OAAO,CAAC,IAAI;IAC3B,EAAE,OAAO,GAAG;QACV,OAAO;IACT;IACA,OAAO;AACT;AACA,IAAI,eAAe,6JAAA,CAAA,UAAK,CAAC,UAAU,CACjC,SAAS,cAAc,KAAK,EAAE,YAAY;IACxC,MAAM,KAAK,OAAO,EAChB,KAAK,EACL,OAAO,YAAY,EACnB,QAAQ,aAAa,EACrB,UAAU,eAAe,EACzB,UAAU,eAAe,EACzB,gBAAgB,qBAAqB,EACrC,aAAa,kBAAkB,EAC/B,UAAU,EACV,UAAU,EACV,aAAa,EACd,GAAG,IAAI,aAAa,UAAU,IAAI;QACjC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,MAAM,KAAK,YAAY,EAAE,KAAK,EAAE,GAAG,IAAI,aAAa,UAAU,IAAI;QAAC;KAAQ;IAC3E,MAAM,SAAS,iBAAiB,OAAO,KAAK,IAAI,cAAc,GAAG;IACjE,MAAM,UAAU,6JAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC7B,MAAM,SAAS,6JAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC5B,MAAM,eAAe,6JAAA,CAAA,UAAK,CAAC,WAAW;gEACpC,CAAC;YACC,QAAQ,OAAO,GAAG;YAClB,IAAI,OAAO,iBAAiB,YAAY;gBACtC,aAAa;YACf,OAAO,IAAI,cAAc;gBACvB,aAAa,OAAO,GAAG;YACzB;QACF;+DACA;QAAC;KAAa;IAEhB,MAAM,CAAC,aAAa,iBAAiB,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IACvD,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,uBAAuB,EAAE,GAAG,UAAU;QACrE;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IACA,6JAAA,CAAA,UAAK,CAAC,SAAS;gDAAC;YACd,IAAI,QAAQ,OAAO,IAAI,MAAM;gBAC3B,MAAM,SAAS,QAAQ,OAAO;gBAC9B,MAAM,MAAM,OAAO,UAAU,CAAC;gBAC9B,IAAI,CAAC,KAAK;oBACR;gBACF;gBACA,IAAI,cAAc;gBAClB,MAAM,QAAQ,OAAO,OAAO;gBAC5B,MAAM,oBAAoB,2BAA2B,QAAQ,UAAU,QAAQ,MAAM,QAAQ,IAAI,MAAM,aAAa,KAAK,KAAK,MAAM,YAAY,KAAK;gBACrJ,IAAI,mBAAmB;oBACrB,IAAI,wBAAwB,UAAU,IAAI,MAAM;wBAC9C,cAAc,gBACZ,OACA,wBAAwB,UAAU;oBAEtC;gBACF;gBACA,MAAM,aAAa,OAAO,gBAAgB,IAAI;gBAC9C,OAAO,MAAM,GAAG,OAAO,KAAK,GAAG,OAAO;gBACtC,MAAM,QAAQ,OAAO,WAAW;gBAChC,IAAI,KAAK,CAAC,OAAO;gBACjB,IAAI,SAAS,GAAG;gBAChB,IAAI,QAAQ,CAAC,GAAG,GAAG,UAAU;gBAC7B,IAAI,SAAS,GAAG;gBAChB,IAAI,iBAAiB;oBACnB,IAAI,IAAI,CAAC,IAAI,OAAO,aAAa,aAAa;gBAChD,OAAO;oBACL,MAAM,OAAO;gEAAC,SAAS,GAAG,EAAE,GAAG;4BAC7B,IAAI,OAAO;wEAAC,SAAS,IAAI,EAAE,GAAG;oCAC5B,IAAI,MAAM;wCACR,IAAI,QAAQ,CAAC,MAAM,QAAQ,MAAM,QAAQ,GAAG;oCAC9C;gCACF;;wBACF;;gBACF;gBACA,IAAI,yBAAyB;oBAC3B,IAAI,WAAW,GAAG,wBAAwB,OAAO;gBACnD;gBACA,IAAI,mBAAmB;oBACrB,IAAI,SAAS,CACX,OACA,wBAAwB,CAAC,GAAG,QAC5B,wBAAwB,CAAC,GAAG,QAC5B,wBAAwB,CAAC,EACzB,wBAAwB,CAAC;gBAE7B;YACF;QACF;;IACA,6JAAA,CAAA,UAAK,CAAC,SAAS;gDAAC;YACd,iBAAiB;QACnB;+CAAG;QAAC;KAAO;IACX,MAAM,cAAc,eAAe;QAAE,QAAQ;QAAM,OAAO;IAAK,GAAG;IAClE,IAAI,MAAM;IACV,IAAI,UAAU,MAAM;QAClB,MAAM,aAAa,GAAG,6JAAA,CAAA,UAAK,CAAC,aAAa,CACvC,OACA;YACE,KAAK;YACL,KAAK;YACL,OAAO;gBAAE,SAAS;YAAO;YACzB,QAAQ;gBACN,iBAAiB;YACnB;YACA,KAAK;YACL,aAAa,2BAA2B,OAAO,KAAK,IAAI,wBAAwB,WAAW;QAC7F;IAEJ;IACA,OAAO,aAAa,GAAG,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,6JAAA,CAAA,UAAK,CAAC,QAAQ,EAAE,MAAM,aAAa,GAAG,6JAAA,CAAA,UAAK,CAAC,aAAa,CAClG,UACA,eAAe;QACb,OAAO;QACP,QAAQ;QACR,OAAO;QACP,KAAK;QACL,MAAM;IACR,GAAG,cACF;AACL;AAEF,aAAa,WAAW,GAAG;AAC3B,IAAI,YAAY,6JAAA,CAAA,UAAK,CAAC,UAAU,CAC9B,SAAS,WAAW,KAAK,EAAE,YAAY;IACrC,MAAM,KAAK,OAAO,EAChB,KAAK,EACL,OAAO,YAAY,EACnB,QAAQ,aAAa,EACrB,UAAU,eAAe,EACzB,UAAU,eAAe,EACzB,gBAAgB,qBAAqB,EACrC,aAAa,kBAAkB,EAC/B,UAAU,EACV,KAAK,EACL,UAAU,EACV,aAAa,EACd,GAAG,IAAI,aAAa,UAAU,IAAI;QACjC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,uBAAuB,EAAE,GAAG,UAAU;QACrE;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IACA,IAAI,cAAc;IAClB,IAAI,QAAQ;IACZ,IAAI,iBAAiB,QAAQ,2BAA2B,MAAM;QAC5D,IAAI,wBAAwB,UAAU,IAAI,MAAM;YAC9C,cAAc,gBACZ,OACA,wBAAwB,UAAU;QAEtC;QACA,QAAQ,aAAa,GAAG,6JAAA,CAAA,UAAK,CAAC,aAAa,CACzC,SACA;YACE,MAAM,cAAc,GAAG;YACvB,QAAQ,wBAAwB,CAAC;YACjC,OAAO,wBAAwB,CAAC;YAChC,GAAG,wBAAwB,CAAC,GAAG;YAC/B,GAAG,wBAAwB,CAAC,GAAG;YAC/B,qBAAqB;YACrB,SAAS,wBAAwB,OAAO;YACxC,aAAa,wBAAwB,WAAW;QAClD;IAEJ;IACA,MAAM,SAAS,aAAa,aAAa;IACzC,OAAO,aAAa,GAAG,6JAAA,CAAA,UAAK,CAAC,aAAa,CACxC,OACA,eAAe;QACb,QAAQ;QACR,OAAO;QACP,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,UAAU;QACtC,KAAK;QACL,MAAM;IACR,GAAG,aACH,CAAC,CAAC,SAAS,aAAa,GAAG,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,SAAS,MAAM,QAC9D,aAAa,GAAG,6JAAA,CAAA,UAAK,CAAC,aAAa,CACjC,QACA;QACE,MAAM;QACN,GAAG,CAAC,MAAM,EAAE,SAAS,CAAC,EAAE,SAAS,GAAG,CAAC;QACrC,gBAAgB;IAClB,IAEF,aAAa,GAAG,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,QAAQ;QAAE,MAAM;QAAS,GAAG;QAAQ,gBAAgB;IAAa,IACrG;AAEJ;AAEF,UAAU,WAAW,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1712, "column": 0}, "map": {"version": 3, "file": "upload.js", "sources": ["file://D%3A/testnext/bond-hub-fe/node_modules/lucide-react/src/icons/upload.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4', key: 'ih7n3h' }],\n  ['polyline', { points: '17 8 12 3 7 8', key: 't8dd8p' }],\n  ['line', { x1: '12', x2: '12', y1: '3', y2: '15', key: 'widbto' }],\n];\n\n/**\n * @component @name Upload\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEgMTV2NGEyIDIgMCAwIDEtMiAySDVhMiAyIDAgMCAxLTItMnYtNCIgLz4KICA8cG9seWxpbmUgcG9pbnRzPSIxNyA4IDEyIDMgNyA4IiAvPgogIDxsaW5lIHgxPSIxMiIgeDI9IjEyIiB5MT0iMyIgeTI9IjE1IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/upload\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Upload = createLucideIcon('upload', __iconNode);\n\nexport default Upload;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA6C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IAC1E;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;QAAA,CAAA;YAAE,QAAQ,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IACvD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CACnE,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1768, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/node_modules/%40radix-ui/react-checkbox/node_modules/%40radix-ui/react-compose-refs/src/compose-refs.tsx"], "sourcesContent": ["import * as React from 'react';\n\ntype PossibleRef<T> = React.Ref<T> | undefined;\n\n/**\n * Set a given ref to a given value\n * This utility takes care of different types of refs: callback refs and RefObject(s)\n */\nfunction setRef<T>(ref: PossibleRef<T>, value: T) {\n  if (typeof ref === 'function') {\n    return ref(value);\n  } else if (ref !== null && ref !== undefined) {\n    ref.current = value;\n  }\n}\n\n/**\n * A utility to compose multiple refs together\n * Accepts callback refs and RefObject(s)\n */\nfunction composeRefs<T>(...refs: PossibleRef<T>[]): React.RefCallback<T> {\n  return (node) => {\n    let hasCleanup = false;\n    const cleanups = refs.map((ref) => {\n      const cleanup = setRef(ref, node);\n      if (!hasCleanup && typeof cleanup == 'function') {\n        hasCleanup = true;\n      }\n      return cleanup;\n    });\n\n    // React <19 will log an error to the console if a callback ref returns a\n    // value. We don't use ref cleanups internally so this will only happen if a\n    // user's ref callback returns a value, which we only expect if they are\n    // using the cleanup functionality added in React 19.\n    if (hasCleanup) {\n      return () => {\n        for (let i = 0; i < cleanups.length; i++) {\n          const cleanup = cleanups[i];\n          if (typeof cleanup == 'function') {\n            cleanup();\n          } else {\n            setRef(refs[i], null);\n          }\n        }\n      };\n    }\n  };\n}\n\n/**\n * A custom hook that composes multiple refs\n * Accepts callback refs and RefObject(s)\n */\nfunction useComposedRefs<T>(...refs: PossibleRef<T>[]): React.RefCallback<T> {\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  return React.useCallback(composeRefs(...refs), refs);\n}\n\nexport { composeRefs, useComposedRefs };\n"], "names": [], "mappings": ";;;;;AAAA,YAAY,WAAW;;AAQvB,SAAS,OAAU,GAAA,EAAqB,KAAA,EAAU;IAChD,IAAI,OAAO,QAAQ,YAAY;QAC7B,OAAO,IAAI,KAAK;IAClB,OAAA,IAAW,QAAQ,QAAQ,QAAQ,KAAA,GAAW;QAC5C,IAAI,OAAA,GAAU;IAChB;AACF;AAMA,SAAS,YAAA,GAAkB,IAAA,EAA8C;IACvE,OAAO,CAAC,SAAS;QACf,IAAI,aAAa;QACjB,MAAM,WAAW,KAAK,GAAA,CAAI,CAAC,QAAQ;YACjC,MAAM,UAAU,OAAO,KAAK,IAAI;YAChC,IAAI,CAAC,cAAc,OAAO,WAAW,YAAY;gBAC/C,aAAa;YACf;YACA,OAAO;QACT,CAAC;QAMD,IAAI,YAAY;YACd,OAAO,MAAM;gBACX,IAAA,IAAS,IAAI,GAAG,IAAI,SAAS,MAAA,EAAQ,IAAK;oBACxC,MAAM,UAAU,QAAA,CAAS,CAAC,CAAA;oBAC1B,IAAI,OAAO,WAAW,YAAY;wBAChC,QAAQ;oBACV,OAAO;wBACL,OAAO,IAAA,CAAK,CAAC,CAAA,EAAG,IAAI;oBACtB;gBACF;YACF;QACF;IACF;AACF;AAMA,SAAS,gBAAA,GAAsB,IAAA,EAA8C;IAE3E,yKAAa,cAAA,EAAY,YAAY,GAAG,IAAI,GAAG,IAAI;AACrD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1817, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/node_modules/%40radix-ui/react-checkbox/node_modules/%40radix-ui/react-context/src/create-context.tsx"], "sourcesContent": ["import * as React from 'react';\n\nfunction createContext<ContextValueType extends object | null>(\n  rootComponentName: string,\n  defaultContext?: ContextValueType\n) {\n  const Context = React.createContext<ContextValueType | undefined>(defaultContext);\n\n  const Provider: React.FC<ContextValueType & { children: React.ReactNode }> = (props) => {\n    const { children, ...context } = props;\n    // Only re-memoize when prop values change\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    const value = React.useMemo(() => context, Object.values(context)) as ContextValueType;\n    return <Context.Provider value={value}>{children}</Context.Provider>;\n  };\n\n  Provider.displayName = rootComponentName + 'Provider';\n\n  function useContext(consumerName: string) {\n    const context = React.useContext(Context);\n    if (context) return context;\n    if (defaultContext !== undefined) return defaultContext;\n    // if a defaultContext wasn't specified, it's a required context.\n    throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n  }\n\n  return [Provider, useContext] as const;\n}\n\n/* -------------------------------------------------------------------------------------------------\n * createContextScope\n * -----------------------------------------------------------------------------------------------*/\n\ntype Scope<C = any> = { [scopeName: string]: React.Context<C>[] } | undefined;\ntype ScopeHook = (scope: Scope) => { [__scopeProp: string]: Scope };\ninterface CreateScope {\n  scopeName: string;\n  (): ScopeHook;\n}\n\nfunction createContextScope(scopeName: string, createContextScopeDeps: CreateScope[] = []) {\n  let defaultContexts: any[] = [];\n\n  /* -----------------------------------------------------------------------------------------------\n   * createContext\n   * ---------------------------------------------------------------------------------------------*/\n\n  function createContext<ContextValueType extends object | null>(\n    rootComponentName: string,\n    defaultContext?: ContextValueType\n  ) {\n    const BaseContext = React.createContext<ContextValueType | undefined>(defaultContext);\n    const index = defaultContexts.length;\n    defaultContexts = [...defaultContexts, defaultContext];\n\n    const Provider: React.FC<\n      ContextValueType & { scope: Scope<ContextValueType>; children: React.ReactNode }\n    > = (props) => {\n      const { scope, children, ...context } = props;\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      // Only re-memoize when prop values change\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      const value = React.useMemo(() => context, Object.values(context)) as ContextValueType;\n      return <Context.Provider value={value}>{children}</Context.Provider>;\n    };\n\n    Provider.displayName = rootComponentName + 'Provider';\n\n    function useContext(consumerName: string, scope: Scope<ContextValueType | undefined>) {\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const context = React.useContext(Context);\n      if (context) return context;\n      if (defaultContext !== undefined) return defaultContext;\n      // if a defaultContext wasn't specified, it's a required context.\n      throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n\n    return [Provider, useContext] as const;\n  }\n\n  /* -----------------------------------------------------------------------------------------------\n   * createScope\n   * ---------------------------------------------------------------------------------------------*/\n\n  const createScope: CreateScope = () => {\n    const scopeContexts = defaultContexts.map((defaultContext) => {\n      return React.createContext(defaultContext);\n    });\n    return function useScope(scope: Scope) {\n      const contexts = scope?.[scopeName] || scopeContexts;\n      return React.useMemo(\n        () => ({ [`__scope${scopeName}`]: { ...scope, [scopeName]: contexts } }),\n        [scope, contexts]\n      );\n    };\n  };\n\n  createScope.scopeName = scopeName;\n  return [createContext, composeContextScopes(createScope, ...createContextScopeDeps)] as const;\n}\n\n/* -------------------------------------------------------------------------------------------------\n * composeContextScopes\n * -----------------------------------------------------------------------------------------------*/\n\nfunction composeContextScopes(...scopes: CreateScope[]) {\n  const baseScope = scopes[0];\n  if (scopes.length === 1) return baseScope;\n\n  const createScope: CreateScope = () => {\n    const scopeHooks = scopes.map((createScope) => ({\n      useScope: createScope(),\n      scopeName: createScope.scopeName,\n    }));\n\n    return function useComposedScopes(overrideScopes) {\n      const nextScopes = scopeHooks.reduce((nextScopes, { useScope, scopeName }) => {\n        // We are calling a hook inside a callback which React warns against to avoid inconsistent\n        // renders, however, scoping doesn't have render side effects so we ignore the rule.\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        const scopeProps = useScope(overrideScopes);\n        const currentScope = scopeProps[`__scope${scopeName}`];\n        return { ...nextScopes, ...currentScope };\n      }, {});\n\n      return React.useMemo(() => ({ [`__scope${baseScope.scopeName}`]: nextScopes }), [nextScopes]);\n    };\n  };\n\n  createScope.scopeName = baseScope.scopeName;\n  return createScope;\n}\n\n/* -----------------------------------------------------------------------------------------------*/\n\nexport { createContext, createContextScope };\nexport type { CreateScope, Scope };\n"], "names": ["createContext", "useContext", "createScope", "nextScopes"], "mappings": ";;;;;AAAA,YAAY,WAAW;AAaZ;;;AAXX,SAASA,eACP,iBAAA,EACA,cAAA,EACA;IACA,MAAM,4KAAgB,gBAAA,EAA4C,cAAc;IAEhF,MAAM,WAAuE,CAAC,UAAU;QACtF,MAAM,EAAE,QAAA,EAAU,GAAG,QAAQ,CAAA,GAAI;QAGjC,MAAM,0KAAc,UAAA;sDAAQ,IAAM;qDAAS,OAAO,MAAA,CAAO,OAAO,CAAC;QACjE,OAAO,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,QAAQ,QAAA,EAAR;YAAiB;YAAe;QAAA,CAAS;IACnD;IAEA,SAAS,WAAA,GAAc,oBAAoB;IAE3C,SAASC,YAAW,YAAA,EAAsB;QACxC,MAAM,4KAAgB,aAAA,EAAW,OAAO;QACxC,IAAI,QAAS,CAAA,OAAO;QACpB,IAAI,mBAAmB,KAAA,EAAW,CAAA,OAAO;QAEzC,MAAM,IAAI,MAAM,CAAA,EAAA,EAAK,YAAY,CAAA,yBAAA,EAA4B,iBAAiB,CAAA,EAAA,CAAI;IACpF;IAEA,OAAO;QAAC;QAAUA,WAAU;KAAA;AAC9B;AAaA,SAAS,mBAAmB,SAAA,EAAmB,yBAAwC,CAAC,CAAA,EAAG;IACzF,IAAI,kBAAyB,CAAC,CAAA;IAM9B,SAASD,eACP,iBAAA,EACA,cAAA,EACA;QACA,MAAM,gLAAoB,gBAAA,EAA4C,cAAc;QACpF,MAAM,QAAQ,gBAAgB,MAAA;QAC9B,kBAAkB,CAAC;eAAG;YAAiB,cAAc;SAAA;QAErD,MAAM,WAEF,CAAC,UAAU;YACb,MAAM,EAAE,KAAA,EAAO,QAAA,EAAU,GAAG,QAAQ,CAAA,GAAI;YACxC,MAAM,UAAU,OAAA,CAAQ,SAAS,CAAA,EAAA,CAAI,KAAK,CAAA,IAAK;YAG/C,MAAM,SAAc,2KAAA;6EAAQ,IAAM;4EAAS,OAAO,MAAA,CAAO,OAAO,CAAC;YACjE,OAAO,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,QAAQ,QAAA,EAAR;gBAAiB;gBAAe;YAAA,CAAS;QACnD;QAEA,SAAS,WAAA,GAAc,oBAAoB;QAE3C,SAASC,YAAW,YAAA,EAAsB,KAAA,EAA4C;YACpF,MAAM,UAAU,OAAA,CAAQ,SAAS,CAAA,EAAA,CAAI,KAAK,CAAA,IAAK;YAC/C,MAAM,WAAgB,8KAAA,EAAW,OAAO;YACxC,IAAI,QAAS,CAAA,OAAO;YACpB,IAAI,mBAAmB,KAAA,EAAW,CAAA,OAAO;YAEzC,MAAM,IAAI,MAAM,CAAA,EAAA,EAAK,YAAY,CAAA,yBAAA,EAA4B,iBAAiB,CAAA,EAAA,CAAI;QACpF;QAEA,OAAO;YAAC;YAAUA,WAAU;SAAA;IAC9B;IAMA,MAAM,cAA2B,MAAM;QACrC,MAAM,gBAAgB,gBAAgB,GAAA,CAAI,CAAC,mBAAmB;YAC5D,yKAAa,gBAAA,EAAc,cAAc;QAC3C,CAAC;QACD,OAAO,SAAS,SAAS,KAAA,EAAc;YACrC,MAAM,WAAW,OAAA,CAAQ,SAAS,CAAA,IAAK;YACvC,yKAAa,UAAA;mEACX,IAAA,CAAO;wBAAE,CAAC,CAAA,OAAA,EAAU,SAAS,EAAE,CAAA,EAAG;4BAAE,GAAG,KAAA;4BAAO,CAAC,SAAS,CAAA,EAAG;wBAAS;oBAAE,CAAA;kEACtE;gBAAC;gBAAO,QAAQ;aAAA;QAEpB;IACF;IAEA,YAAY,SAAA,GAAY;IACxB,OAAO;QAACD;QAAe,qBAAqB,aAAa,GAAG,sBAAsB,CAAC;KAAA;AACrF;AAMA,SAAS,qBAAA,GAAwB,MAAA,EAAuB;IACtD,MAAM,YAAY,MAAA,CAAO,CAAC,CAAA;IAC1B,IAAI,OAAO,MAAA,KAAW,EAAG,CAAA,OAAO;IAEhC,MAAM,cAA2B,MAAM;QACrC,MAAM,aAAa,OAAO,GAAA,CAAI,CAACE,eAAAA,CAAiB;gBAC9C,UAAUA,aAAY;gBACtB,WAAWA,aAAY,SAAA;YACzB,CAAA,CAAE;QAEF,OAAO,SAAS,kBAAkB,cAAA,EAAgB;YAChD,MAAM,aAAa,WAAW,MAAA,CAAO,CAACC,aAAY,EAAE,QAAA,EAAU,SAAA,CAAU,CAAA,KAAM;gBAI5E,MAAM,aAAa,SAAS,cAAc;gBAC1C,MAAM,eAAe,UAAA,CAAW,CAAA,OAAA,EAAU,SAAS,EAAE,CAAA;gBACrD,OAAO;oBAAE,GAAGA,WAAAA;oBAAY,GAAG,YAAA;gBAAa;YAC1C,GAAG,CAAC,CAAC;YAEL,yKAAa,UAAA;8EAAQ,IAAA,CAAO;wBAAE,CAAC,CAAA,OAAA,EAAU,UAAU,SAAS,EAAE,CAAA,EAAG;oBAAW,CAAA;6EAAI;gBAAC,UAAU;aAAC;QAC9F;IACF;IAEA,YAAY,SAAA,GAAY,UAAU,SAAA;IAClC,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1945, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/node_modules/%40radix-ui/react-checkbox/node_modules/%40radix-ui/primitive/src/primitive.tsx"], "sourcesContent": ["function composeEventHandlers<E extends { defaultPrevented: boolean }>(\n  originalEventHandler?: (event: E) => void,\n  ourEventHandler?: (event: E) => void,\n  { checkForDefaultPrevented = true } = {}\n) {\n  return function handleEvent(event: E) {\n    originalEventHandler?.(event);\n\n    if (checkForDefaultPrevented === false || !event.defaultPrevented) {\n      return ourEventHandler?.(event);\n    }\n  };\n}\n\nexport { composeEventHandlers };\n"], "names": [], "mappings": ";;;;AAAA,SAAS,qBACP,oBAAA,EACA,eAAA,EACA,EAAE,2BAA2B,IAAA,CAAK,CAAA,GAAI,CAAC,CAAA,EACvC;IACA,OAAO,SAAS,YAAY,KAAA,EAAU;QACpC,uBAAuB,KAAK;QAE5B,IAAI,6BAA6B,SAAS,CAAC,MAAM,gBAAA,EAAkB;YACjE,OAAO,kBAAkB,KAAK;QAChC;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1965, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/node_modules/%40radix-ui/react-checkbox/node_modules/%40radix-ui/react-use-callback-ref/src/use-callback-ref.tsx"], "sourcesContent": ["import * as React from 'react';\n\n/**\n * A custom hook that converts a callback to a ref to avoid triggering re-renders when passed as a\n * prop or avoid re-executing effects when passed as a dependency\n */\nfunction useCallbackRef<T extends (...args: any[]) => any>(callback: T | undefined): T {\n  const callbackRef = React.useRef(callback);\n\n  React.useEffect(() => {\n    callbackRef.current = callback;\n  });\n\n  // https://github.com/facebook/react/issues/19240\n  return React.useMemo(() => ((...args) => callbackRef.current?.(...args)) as T, []);\n}\n\nexport { useCallbackRef };\n"], "names": [], "mappings": ";;;;AAAA,YAAY,WAAW;;AAMvB,SAAS,eAAkD,QAAA,EAA4B;IACrF,MAAM,gLAAoB,SAAA,EAAO,QAAQ;sKAEnC,YAAA;oCAAU,MAAM;YACpB,YAAY,OAAA,GAAU;QACxB,CAAC;;IAGD,yKAAa,UAAA;kCAAQ;0CAAO,CAAA,GAAI,OAAS,YAAY,OAAA,GAAU,GAAG,IAAI;;iCAAS,CAAC,CAAC;AACnF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1992, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/node_modules/%40radix-ui/react-checkbox/node_modules/%40radix-ui/react-use-controllable-state/src/use-controllable-state.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\n\ntype UseControllableStateParams<T> = {\n  prop?: T | undefined;\n  defaultProp?: T | undefined;\n  onChange?: (state: T) => void;\n};\n\ntype SetStateFn<T> = (prevState?: T) => T;\n\nfunction useControllableState<T>({\n  prop,\n  defaultProp,\n  onChange = () => {},\n}: UseControllableStateParams<T>) {\n  const [uncontrolledProp, setUncontrolledProp] = useUncontrolledState({ defaultProp, onChange });\n  const isControlled = prop !== undefined;\n  const value = isControlled ? prop : uncontrolledProp;\n  const handleChange = useCallbackRef(onChange);\n\n  const setValue: React.Dispatch<React.SetStateAction<T | undefined>> = React.useCallback(\n    (nextValue) => {\n      if (isControlled) {\n        const setter = nextValue as SetStateFn<T>;\n        const value = typeof nextValue === 'function' ? setter(prop) : nextValue;\n        if (value !== prop) handleChange(value as T);\n      } else {\n        setUncontrolledProp(nextValue);\n      }\n    },\n    [isControlled, prop, setUncontrolledProp, handleChange]\n  );\n\n  return [value, setValue] as const;\n}\n\nfunction useUncontrolledState<T>({\n  defaultProp,\n  onChange,\n}: Omit<UseControllableStateParams<T>, 'prop'>) {\n  const uncontrolledState = React.useState<T | undefined>(defaultProp);\n  const [value] = uncontrolledState;\n  const prevValueRef = React.useRef(value);\n  const handleChange = useCallbackRef(onChange);\n\n  React.useEffect(() => {\n    if (prevValueRef.current !== value) {\n      handleChange(value as T);\n      prevValueRef.current = value;\n    }\n  }, [value, prevValueRef, handleChange]);\n\n  return uncontrolledState;\n}\n\nexport { useControllableState };\n"], "names": ["value"], "mappings": ";;;;AAAA,YAAY,WAAW;AACvB,SAAS,sBAAsB;;;AAU/B,SAAS,qBAAwB,EAC/B,IAAA,EACA,WAAA,EACA,WAAW,KAAO,CAAD,AAAC,EACpB,EAAkC;IAChC,MAAM,CAAC,kBAAkB,mBAAmB,CAAA,GAAI,qBAAqB;QAAE;QAAa;IAAS,CAAC;IAC9F,MAAM,eAAe,SAAS,KAAA;IAC9B,MAAM,QAAQ,eAAe,OAAO;IACpC,MAAM,+PAAe,iBAAA,EAAe,QAAQ;IAE5C,MAAM,YAAsE,+KAAA;sDAC1E,CAAC,cAAc;YACb,IAAI,cAAc;gBAChB,MAAM,SAAS;gBACf,MAAMA,SAAQ,OAAO,cAAc,aAAa,OAAO,IAAI,IAAI;gBAC/D,IAAIA,WAAU,KAAM,CAAA,aAAaA,MAAU;YAC7C,OAAO;gBACL,oBAAoB,SAAS;YAC/B;QACF;qDACA;QAAC;QAAc;QAAM;QAAqB,YAAY;KAAA;IAGxD,OAAO;QAAC;QAAO,QAAQ;KAAA;AACzB;AAEA,SAAS,qBAAwB,EAC/B,WAAA,EACA,QAAA,EACF,EAAgD;IAC9C,MAAM,sLAA0B,WAAA,EAAwB,WAAW;IACnE,MAAM,CAAC,KAAK,CAAA,GAAI;IAChB,MAAM,iLAAqB,SAAA,EAAO,KAAK;IACvC,MAAM,gBAAe,gQAAA,EAAe,QAAQ;sKAEtC,YAAA;0CAAU,MAAM;YACpB,IAAI,aAAa,OAAA,KAAY,OAAO;gBAClC,aAAa,KAAU;gBACvB,aAAa,OAAA,GAAU;YACzB;QACF;yCAAG;QAAC;QAAO;QAAc,YAAY;KAAC;IAEtC,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2056, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/node_modules/%40radix-ui/react-checkbox/node_modules/%40radix-ui/react-use-previous/src/use-previous.tsx"], "sourcesContent": ["import * as React from 'react';\n\nfunction usePrevious<T>(value: T) {\n  const ref = React.useRef({ value, previous: value });\n\n  // We compare values before making an update to ensure that\n  // a change has been made. This ensures the previous value is\n  // persisted correctly between renders.\n  return React.useMemo(() => {\n    if (ref.current.value !== value) {\n      ref.current.previous = ref.current.value;\n      ref.current.value = value;\n    }\n    return ref.current.previous;\n  }, [value]);\n}\n\nexport { usePrevious };\n"], "names": [], "mappings": ";;;;AAAA,YAAY,WAAW;;AAEvB,SAAS,YAAe,KAAA,EAAU;IAChC,MAAM,wKAAY,SAAA,EAAO;QAAE;QAAO,UAAU;IAAM,CAAC;IAKnD,yKAAa,UAAA;+BAAQ,MAAM;YACzB,IAAI,IAAI,OAAA,CAAQ,KAAA,KAAU,OAAO;gBAC/B,IAAI,OAAA,CAAQ,QAAA,GAAW,IAAI,OAAA,CAAQ,KAAA;gBACnC,IAAI,OAAA,CAAQ,KAAA,GAAQ;YACtB;YACA,OAAO,IAAI,OAAA,CAAQ,QAAA;QACrB;8BAAG;QAAC,KAAK;KAAC;AACZ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2087, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/node_modules/%40radix-ui/react-checkbox/node_modules/%40radix-ui/react-use-layout-effect/src/use-layout-effect.tsx"], "sourcesContent": ["import * as React from 'react';\n\n/**\n * On the server, <PERSON>act emits a warning when calling `useLayoutEffect`.\n * This is because neither `useLayoutEffect` nor `useEffect` run on the server.\n * We use this safe version which suppresses the warning by replacing it with a noop on the server.\n *\n * See: https://reactjs.org/docs/hooks-reference.html#uselayouteffect\n */\nconst useLayoutEffect = globalThis?.document ? React.useLayoutEffect : () => {};\n\nexport { useLayoutEffect };\n"], "names": ["useLayoutEffect"], "mappings": ";;;;AAAA,YAAY,WAAW;;AASvB,IAAMA,mBAAkB,YAAY,yKAAiB,kBAAA,GAAkB,KAAO,CAAD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2102, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/node_modules/%40radix-ui/react-checkbox/node_modules/%40radix-ui/react-use-size/src/use-size.tsx"], "sourcesContent": ["/// <reference types=\"resize-observer-browser\" />\n\nimport * as React from 'react';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\n\nfunction useSize(element: HTMLElement | null) {\n  const [size, setSize] = React.useState<{ width: number; height: number } | undefined>(undefined);\n\n  useLayoutEffect(() => {\n    if (element) {\n      // provide size as early as possible\n      setSize({ width: element.offsetWidth, height: element.offsetHeight });\n\n      const resizeObserver = new ResizeObserver((entries) => {\n        if (!Array.isArray(entries)) {\n          return;\n        }\n\n        // Since we only observe the one element, we don't need to loop over the\n        // array\n        if (!entries.length) {\n          return;\n        }\n\n        const entry = entries[0];\n        let width: number;\n        let height: number;\n\n        if ('borderBoxSize' in entry) {\n          const borderSizeEntry = entry['borderBoxSize'];\n          // iron out differences between browsers\n          const borderSize = Array.isArray(borderSizeEntry) ? borderSizeEntry[0] : borderSizeEntry;\n          width = borderSize['inlineSize'];\n          height = borderSize['blockSize'];\n        } else {\n          // for browsers that don't support `borderBoxSize`\n          // we calculate it ourselves to get the correct border box.\n          width = element.offsetWidth;\n          height = element.offsetHeight;\n        }\n\n        setSize({ width, height });\n      });\n\n      resizeObserver.observe(element, { box: 'border-box' });\n\n      return () => resizeObserver.unobserve(element);\n    } else {\n      // We only want to reset to `undefined` when the element becomes `null`,\n      // not if it changes to another element.\n      setSize(undefined);\n    }\n  }, [element]);\n\n  return size;\n}\n\nexport { useSize };\n"], "names": [], "mappings": ";;;;AAEA,YAAY,WAAW;AACvB,SAAS,uBAAuB;;;AAEhC,SAAS,QAAQ,OAAA,EAA6B;IAC5C,MAAM,CAAC,MAAM,OAAO,CAAA,qKAAU,WAAA,EAAwD,KAAA,CAAS;IAE/F,CAAA,GAAA,4OAAA,CAAA,kBAAA;mCAAgB,MAAM;YACpB,IAAI,SAAS;gBAEX,QAAQ;oBAAE,OAAO,QAAQ,WAAA;oBAAa,QAAQ,QAAQ,YAAA;gBAAa,CAAC;gBAEpE,MAAM,iBAAiB,IAAI;+CAAe,CAAC,YAAY;wBACrD,IAAI,CAAC,MAAM,OAAA,CAAQ,OAAO,GAAG;4BAC3B;wBACF;wBAIA,IAAI,CAAC,QAAQ,MAAA,EAAQ;4BACnB;wBACF;wBAEA,MAAM,QAAQ,OAAA,CAAQ,CAAC,CAAA;wBACvB,IAAI;wBACJ,IAAI;wBAEJ,IAAI,mBAAmB,OAAO;4BAC5B,MAAM,kBAAkB,KAAA,CAAM,eAAe,CAAA;4BAE7C,MAAM,aAAa,MAAM,OAAA,CAAQ,eAAe,IAAI,eAAA,CAAgB,CAAC,CAAA,GAAI;4BACzE,QAAQ,UAAA,CAAW,YAAY,CAAA;4BAC/B,SAAS,UAAA,CAAW,WAAW,CAAA;wBACjC,OAAO;4BAGL,QAAQ,QAAQ,WAAA;4BAChB,SAAS,QAAQ,YAAA;wBACnB;wBAEA,QAAQ;4BAAE;4BAAO;wBAAO,CAAC;oBAC3B,CAAC;;gBAED,eAAe,OAAA,CAAQ,SAAS;oBAAE,KAAK;gBAAa,CAAC;gBAErD;+CAAO,IAAM,eAAe,SAAA,CAAU,OAAO;;YAC/C,OAAO;gBAGL,QAAQ,KAAA,CAAS;YACnB;QACF;kCAAG;QAAC,OAAO;KAAC;IAEZ,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2168, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/node_modules/%40radix-ui/react-checkbox/node_modules/%40radix-ui/react-presence/src/presence.tsx", "file://D%3A/testnext/bond-hub-fe/node_modules/%40radix-ui/react-checkbox/node_modules/%40radix-ui/react-presence/src/use-state-machine.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { useStateMachine } from './use-state-machine';\n\ninterface PresenceProps {\n  children: React.ReactElement | ((props: { present: boolean }) => React.ReactElement);\n  present: boolean;\n}\n\nconst Presence: React.FC<PresenceProps> = (props) => {\n  const { present, children } = props;\n  const presence = usePresence(present);\n\n  const child = (\n    typeof children === 'function'\n      ? children({ present: presence.isPresent })\n      : React.Children.only(children)\n  ) as React.ReactElement<{ ref?: React.Ref<HTMLElement> }>;\n\n  const ref = useComposedRefs(presence.ref, getElementRef(child));\n  const forceMount = typeof children === 'function';\n  return forceMount || presence.isPresent ? React.cloneElement(child, { ref }) : null;\n};\n\nPresence.displayName = 'Presence';\n\n/* -------------------------------------------------------------------------------------------------\n * usePresence\n * -----------------------------------------------------------------------------------------------*/\n\nfunction usePresence(present: boolean) {\n  const [node, setNode] = React.useState<HTMLElement>();\n  const stylesRef = React.useRef<CSSStyleDeclaration>({} as any);\n  const prevPresentRef = React.useRef(present);\n  const prevAnimationNameRef = React.useRef<string>('none');\n  const initialState = present ? 'mounted' : 'unmounted';\n  const [state, send] = useStateMachine(initialState, {\n    mounted: {\n      UNMOUNT: 'unmounted',\n      ANIMATION_OUT: 'unmountSuspended',\n    },\n    unmountSuspended: {\n      MOUNT: 'mounted',\n      ANIMATION_END: 'unmounted',\n    },\n    unmounted: {\n      MOUNT: 'mounted',\n    },\n  });\n\n  React.useEffect(() => {\n    const currentAnimationName = getAnimationName(stylesRef.current);\n    prevAnimationNameRef.current = state === 'mounted' ? currentAnimationName : 'none';\n  }, [state]);\n\n  useLayoutEffect(() => {\n    const styles = stylesRef.current;\n    const wasPresent = prevPresentRef.current;\n    const hasPresentChanged = wasPresent !== present;\n\n    if (hasPresentChanged) {\n      const prevAnimationName = prevAnimationNameRef.current;\n      const currentAnimationName = getAnimationName(styles);\n\n      if (present) {\n        send('MOUNT');\n      } else if (currentAnimationName === 'none' || styles?.display === 'none') {\n        // If there is no exit animation or the element is hidden, animations won't run\n        // so we unmount instantly\n        send('UNMOUNT');\n      } else {\n        /**\n         * When `present` changes to `false`, we check changes to animation-name to\n         * determine whether an animation has started. We chose this approach (reading\n         * computed styles) because there is no `animationrun` event and `animationstart`\n         * fires after `animation-delay` has expired which would be too late.\n         */\n        const isAnimating = prevAnimationName !== currentAnimationName;\n\n        if (wasPresent && isAnimating) {\n          send('ANIMATION_OUT');\n        } else {\n          send('UNMOUNT');\n        }\n      }\n\n      prevPresentRef.current = present;\n    }\n  }, [present, send]);\n\n  useLayoutEffect(() => {\n    if (node) {\n      let timeoutId: number;\n      const ownerWindow = node.ownerDocument.defaultView ?? window;\n      /**\n       * Triggering an ANIMATION_OUT during an ANIMATION_IN will fire an `animationcancel`\n       * event for ANIMATION_IN after we have entered `unmountSuspended` state. So, we\n       * make sure we only trigger ANIMATION_END for the currently active animation.\n       */\n      const handleAnimationEnd = (event: AnimationEvent) => {\n        const currentAnimationName = getAnimationName(stylesRef.current);\n        const isCurrentAnimation = currentAnimationName.includes(event.animationName);\n        if (event.target === node && isCurrentAnimation) {\n          // With React 18 concurrency this update is applied a frame after the\n          // animation ends, creating a flash of visible content. By setting the\n          // animation fill mode to \"forwards\", we force the node to keep the\n          // styles of the last keyframe, removing the flash.\n          //\n          // Previously we flushed the update via ReactDom.flushSync, but with\n          // exit animations this resulted in the node being removed from the\n          // DOM before the synthetic animationEnd event was dispatched, meaning\n          // user-provided event handlers would not be called.\n          // https://github.com/radix-ui/primitives/pull/1849\n          send('ANIMATION_END');\n          if (!prevPresentRef.current) {\n            const currentFillMode = node.style.animationFillMode;\n            node.style.animationFillMode = 'forwards';\n            // Reset the style after the node had time to unmount (for cases\n            // where the component chooses not to unmount). Doing this any\n            // sooner than `setTimeout` (e.g. with `requestAnimationFrame`)\n            // still causes a flash.\n            timeoutId = ownerWindow.setTimeout(() => {\n              if (node.style.animationFillMode === 'forwards') {\n                node.style.animationFillMode = currentFillMode;\n              }\n            });\n          }\n        }\n      };\n      const handleAnimationStart = (event: AnimationEvent) => {\n        if (event.target === node) {\n          // if animation occurred, store its name as the previous animation.\n          prevAnimationNameRef.current = getAnimationName(stylesRef.current);\n        }\n      };\n      node.addEventListener('animationstart', handleAnimationStart);\n      node.addEventListener('animationcancel', handleAnimationEnd);\n      node.addEventListener('animationend', handleAnimationEnd);\n      return () => {\n        ownerWindow.clearTimeout(timeoutId);\n        node.removeEventListener('animationstart', handleAnimationStart);\n        node.removeEventListener('animationcancel', handleAnimationEnd);\n        node.removeEventListener('animationend', handleAnimationEnd);\n      };\n    } else {\n      // Transition to the unmounted state if the node is removed prematurely.\n      // We avoid doing so during cleanup as the node may change but still exist.\n      send('ANIMATION_END');\n    }\n  }, [node, send]);\n\n  return {\n    isPresent: ['mounted', 'unmountSuspended'].includes(state),\n    ref: React.useCallback((node: HTMLElement) => {\n      if (node) stylesRef.current = getComputedStyle(node);\n      setNode(node);\n    }, []),\n  };\n}\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction getAnimationName(styles?: CSSStyleDeclaration) {\n  return styles?.animationName || 'none';\n}\n\n// Before React 19 accessing `element.props.ref` will throw a warning and suggest using `element.ref`\n// After React 19 accessing `element.ref` does the opposite.\n// https://github.com/facebook/react/pull/28348\n//\n// Access the ref using the method that doesn't yield a warning.\nfunction getElementRef(element: React.ReactElement<{ ref?: React.Ref<unknown> }>) {\n  // React <=18 in DEV\n  let getter = Object.getOwnPropertyDescriptor(element.props, 'ref')?.get;\n  let mayWarn = getter && 'isReactWarning' in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return (element as any).ref;\n  }\n\n  // React 19 in DEV\n  getter = Object.getOwnPropertyDescriptor(element, 'ref')?.get;\n  mayWarn = getter && 'isReactWarning' in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n\n  // Not DEV\n  return element.props.ref || (element as any).ref;\n}\n\nconst Root = Presence;\n\nexport {\n  Presence,\n  //\n  Root,\n};\nexport type { PresenceProps };\n", "import * as React from 'react';\n\ntype Machine<S> = { [k: string]: { [k: string]: S } };\ntype MachineState<T> = keyof T;\ntype MachineEvent<T> = keyof UnionToIntersection<T[keyof T]>;\n\n// 🤯 https://fettblog.eu/typescript-union-to-intersection/\ntype UnionToIntersection<T> = (T extends any ? (x: T) => any : never) extends (x: infer R) => any\n  ? R\n  : never;\n\nexport function useStateMachine<M>(\n  initialState: MachineState<M>,\n  machine: M & Machine<MachineState<M>>\n) {\n  return React.useReducer((state: MachineState<M>, event: MachineEvent<M>): MachineState<M> => {\n    const nextState = (machine[state] as any)[event];\n    return nextState ?? state;\n  }, initialState);\n}\n"], "names": ["React", "node"], "mappings": ";;;;;AAAA,YAAYA,YAAW;AACvB,SAAS,uBAAuB;AAChC,SAAS,uBAAuB;;;;;;ACSzB,SAAS,gBACd,YAAA,EACA,OAAA,EACA;IACA,yKAAa,aAAA;sCAAW,CAAC,OAAwB,UAA4C;YAC3F,MAAM,YAAa,OAAA,CAAQ,KAAK,CAAA,CAAU,KAAK,CAAA;YAC/C,OAAO,aAAa;QACtB;qCAAG,YAAY;AACjB;;ADTA,IAAM,WAAoC,CAAC,UAAU;IACnD,MAAM,EAAE,OAAA,EAAS,QAAA,CAAS,CAAA,GAAI;IAC9B,MAAM,WAAW,YAAY,OAAO;IAEpC,MAAM,QACJ,OAAO,aAAa,aAChB,SAAS;QAAE,SAAS,SAAS,SAAA;IAAU,CAAC,kKAClC,WAAA,CAAS,IAAA,CAAK,QAAQ;IAGlC,MAAM,UAAM,uPAAA,EAAgB,SAAS,GAAA,EAAK,cAAc,KAAK,CAAC;IAC9D,MAAM,aAAa,OAAO,aAAa;IACvC,OAAO,cAAc,SAAS,SAAA,GAAkB,iLAAA,EAAa,OAAO;QAAE;IAAI,CAAC,IAAI;AACjF;AAEA,SAAS,WAAA,GAAc;AAMvB,SAAS,YAAY,OAAA,EAAkB;IACrC,MAAM,CAAC,MAAM,OAAO,CAAA,qKAAU,WAAA,CAAsB;IACpD,MAAM,gBAAkB,uKAAA,EAA4B,CAAC,CAAQ;IAC7D,MAAM,mLAAuB,SAAA,EAAO,OAAO;IAC3C,MAAM,uBAA6B,2KAAA,EAAe,MAAM;IACxD,MAAM,eAAe,UAAU,YAAY;IAC3C,MAAM,CAAC,OAAO,IAAI,CAAA,GAAI,gBAAgB,cAAc;QAClD,SAAS;YACP,SAAS;YACT,eAAe;QACjB;QACA,kBAAkB;YAChB,OAAO;YACP,eAAe;QACjB;QACA,WAAW;YACT,OAAO;QACT;IACF,CAAC;qKAEK,aAAA;iCAAU,MAAM;YACpB,MAAM,uBAAuB,iBAAiB,UAAU,OAAO;YAC/D,qBAAqB,OAAA,GAAU,UAAU,YAAY,uBAAuB;QAC9E;gCAAG;QAAC,KAAK;KAAC;IAEV,CAAA,GAAA,4OAAA,CAAA,kBAAA;uCAAgB,MAAM;YACpB,MAAM,SAAS,UAAU,OAAA;YACzB,MAAM,aAAa,eAAe,OAAA;YAClC,MAAM,oBAAoB,eAAe;YAEzC,IAAI,mBAAmB;gBACrB,MAAM,oBAAoB,qBAAqB,OAAA;gBAC/C,MAAM,uBAAuB,iBAAiB,MAAM;gBAEpD,IAAI,SAAS;oBACX,KAAK,OAAO;gBACd,OAAA,IAAW,yBAAyB,UAAU,QAAQ,YAAY,QAAQ;oBAGxE,KAAK,SAAS;gBAChB,OAAO;oBAOL,MAAM,cAAc,sBAAsB;oBAE1C,IAAI,cAAc,aAAa;wBAC7B,KAAK,eAAe;oBACtB,OAAO;wBACL,KAAK,SAAS;oBAChB;gBACF;gBAEA,eAAe,OAAA,GAAU;YAC3B;QACF;sCAAG;QAAC;QAAS,IAAI;KAAC;IAElB,CAAA,GAAA,4OAAA,CAAA,kBAAA;uCAAgB,MAAM;YACpB,IAAI,MAAM;gBACR,IAAI;gBACJ,MAAM,cAAc,KAAK,aAAA,CAAc,WAAA,IAAe;gBAMtD,MAAM;sEAAqB,CAAC,UAA0B;wBACpD,MAAM,uBAAuB,iBAAiB,UAAU,OAAO;wBAC/D,MAAM,qBAAqB,qBAAqB,QAAA,CAAS,MAAM,aAAa;wBAC5E,IAAI,MAAM,MAAA,KAAW,QAAQ,oBAAoB;4BAW/C,KAAK,eAAe;4BACpB,IAAI,CAAC,eAAe,OAAA,EAAS;gCAC3B,MAAM,kBAAkB,KAAK,KAAA,CAAM,iBAAA;gCACnC,KAAK,KAAA,CAAM,iBAAA,GAAoB;gCAK/B,YAAY,YAAY,UAAA;sFAAW,MAAM;wCACvC,IAAI,KAAK,KAAA,CAAM,iBAAA,KAAsB,YAAY;4CAC/C,KAAK,KAAA,CAAM,iBAAA,GAAoB;wCACjC;oCACF,CAAC;;4BACH;wBACF;oBACF;;gBACA,MAAM;wEAAuB,CAAC,UAA0B;wBACtD,IAAI,MAAM,MAAA,KAAW,MAAM;4BAEzB,qBAAqB,OAAA,GAAU,iBAAiB,UAAU,OAAO;wBACnE;oBACF;;gBACA,KAAK,gBAAA,CAAiB,kBAAkB,oBAAoB;gBAC5D,KAAK,gBAAA,CAAiB,mBAAmB,kBAAkB;gBAC3D,KAAK,gBAAA,CAAiB,gBAAgB,kBAAkB;gBACxD;mDAAO,MAAM;wBACX,YAAY,YAAA,CAAa,SAAS;wBAClC,KAAK,mBAAA,CAAoB,kBAAkB,oBAAoB;wBAC/D,KAAK,mBAAA,CAAoB,mBAAmB,kBAAkB;wBAC9D,KAAK,mBAAA,CAAoB,gBAAgB,kBAAkB;oBAC7D;;YACF,OAAO;gBAGL,KAAK,eAAe;YACtB;QACF;sCAAG;QAAC;QAAM,IAAI;KAAC;IAEf,OAAO;QACL,WAAW;YAAC;YAAW,kBAAkB;SAAA,CAAE,QAAA,CAAS,KAAK;QACzD,uKAAW,cAAA;uCAAY,CAACC,UAAsB;gBAC5C,IAAIA,MAAM,CAAA,UAAU,OAAA,GAAU,iBAAiBA,KAAI;gBACnD,QAAQA,KAAI;YACd;sCAAG,CAAC,CAAC;IACP;AACF;AAIA,SAAS,iBAAiB,MAAA,EAA8B;IACtD,OAAO,QAAQ,iBAAiB;AAClC;AAOA,SAAS,cAAc,OAAA,EAA2D;IAEhF,IAAI,SAAS,OAAO,wBAAA,CAAyB,QAAQ,KAAA,EAAO,KAAK,GAAG;IACpE,IAAI,UAAU,UAAU,oBAAoB,UAAU,OAAO,cAAA;IAC7D,IAAI,SAAS;QACX,OAAQ,QAAgB,GAAA;IAC1B;IAGA,SAAS,OAAO,wBAAA,CAAyB,SAAS,KAAK,GAAG;IAC1D,UAAU,UAAU,oBAAoB,UAAU,OAAO,cAAA;IACzD,IAAI,SAAS;QACX,OAAO,QAAQ,KAAA,CAAM,GAAA;IACvB;IAGA,OAAO,QAAQ,KAAA,CAAM,GAAA,IAAQ,QAAgB,GAAA;AAC/C;AAEA,IAAM,OAAO", "ignoreList": [0, 1], "debugId": null}}, {"offset": {"line": 2346, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/node_modules/%40radix-ui/react-checkbox/node_modules/%40radix-ui/react-slot/src/slot.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeRefs } from '@radix-ui/react-compose-refs';\n\n/* -------------------------------------------------------------------------------------------------\n * Slot\n * -----------------------------------------------------------------------------------------------*/\n\ninterface SlotProps extends React.HTMLAttributes<HTMLElement> {\n  children?: React.ReactNode;\n}\n\n/* @__NO_SIDE_EFFECTS__ */ export function createSlot(ownerName: string) {\n  const SlotClone = createSlotClone(ownerName);\n  const Slot = React.forwardRef<HTMLElement, SlotProps>((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n    const childrenArray = React.Children.toArray(children);\n    const slottable = childrenArray.find(isSlottable);\n\n    if (slottable) {\n      // the new element to render is the one passed as a child of `Slottable`\n      const newElement = slottable.props.children;\n\n      const newChildren = childrenArray.map((child) => {\n        if (child === slottable) {\n          // because the new element will be the one rendered, we are only interested\n          // in grabbing its children (`newElement.props.children`)\n          if (React.Children.count(newElement) > 1) return React.Children.only(null);\n          return React.isValidElement(newElement)\n            ? (newElement.props as { children: React.ReactNode }).children\n            : null;\n        } else {\n          return child;\n        }\n      });\n\n      return (\n        <SlotClone {...slotProps} ref={forwardedRef}>\n          {React.isValidElement(newElement)\n            ? React.cloneElement(newElement, undefined, newChildren)\n            : null}\n        </SlotClone>\n      );\n    }\n\n    return (\n      <SlotClone {...slotProps} ref={forwardedRef}>\n        {children}\n      </SlotClone>\n    );\n  });\n\n  Slot.displayName = `${ownerName}.Slot`;\n  return Slot;\n}\n\nconst Slot = createSlot('Slot');\n\n/* -------------------------------------------------------------------------------------------------\n * SlotClone\n * -----------------------------------------------------------------------------------------------*/\n\ninterface SlotCloneProps {\n  children: React.ReactNode;\n}\n\n/* @__NO_SIDE_EFFECTS__ */ function createSlotClone(ownerName: string) {\n  const SlotClone = React.forwardRef<any, SlotCloneProps>((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n\n    if (React.isValidElement(children)) {\n      const childrenRef = getElementRef(children);\n      const props = mergeProps(slotProps, children.props as AnyProps);\n      // do not pass ref to React.Fragment for React 19 compatibility\n      if (children.type !== React.Fragment) {\n        props.ref = forwardedRef ? composeRefs(forwardedRef, childrenRef) : childrenRef;\n      }\n      return React.cloneElement(children, props);\n    }\n\n    return React.Children.count(children) > 1 ? React.Children.only(null) : null;\n  });\n\n  SlotClone.displayName = `${ownerName}.SlotClone`;\n  return SlotClone;\n}\n\n/* -------------------------------------------------------------------------------------------------\n * Slottable\n * -----------------------------------------------------------------------------------------------*/\n\nconst SLOTTABLE_IDENTIFIER = Symbol('radix.slottable');\n\ninterface SlottableProps {\n  children: React.ReactNode;\n}\n\ninterface SlottableComponent extends React.FC<SlottableProps> {\n  __radixId: symbol;\n}\n\n/* @__NO_SIDE_EFFECTS__ */ export function createSlottable(ownerName: string) {\n  const Slottable: SlottableComponent = ({ children }) => {\n    return <>{children}</>;\n  };\n  Slottable.displayName = `${ownerName}.Slottable`;\n  Slottable.__radixId = SLOTTABLE_IDENTIFIER;\n  return Slottable;\n}\n\nconst Slottable = createSlottable('Slottable');\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype AnyProps = Record<string, any>;\n\nfunction isSlottable(\n  child: React.ReactNode\n): child is React.ReactElement<SlottableProps, typeof Slottable> {\n  return (\n    React.isValidElement(child) &&\n    typeof child.type === 'function' &&\n    '__radixId' in child.type &&\n    child.type.__radixId === SLOTTABLE_IDENTIFIER\n  );\n}\n\nfunction mergeProps(slotProps: AnyProps, childProps: AnyProps) {\n  // all child props should override\n  const overrideProps = { ...childProps };\n\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      // if the handler exists on both, we compose them\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args: unknown[]) => {\n          childPropValue(...args);\n          slotPropValue(...args);\n        };\n      }\n      // but if it exists only on the slot, we use only this one\n      else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    }\n    // if it's `style`, we merge them\n    else if (propName === 'style') {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === 'className') {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(' ');\n    }\n  }\n\n  return { ...slotProps, ...overrideProps };\n}\n\n// Before React 19 accessing `element.props.ref` will throw a warning and suggest using `element.ref`\n// After React 19 accessing `element.ref` does the opposite.\n// https://github.com/facebook/react/pull/28348\n//\n// Access the ref using the method that doesn't yield a warning.\nfunction getElementRef(element: React.ReactElement) {\n  // React <=18 in DEV\n  let getter = Object.getOwnPropertyDescriptor(element.props, 'ref')?.get;\n  let mayWarn = getter && 'isReactWarning' in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return (element as any).ref;\n  }\n\n  // React 19 in DEV\n  getter = Object.getOwnPropertyDescriptor(element, 'ref')?.get;\n  mayWarn = getter && 'isReactWarning' in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return (element.props as { ref?: React.Ref<unknown> }).ref;\n  }\n\n  // Not DEV\n  return (element.props as { ref?: React.Ref<unknown> }).ref || (element as any).ref;\n}\n\nexport {\n  Slot,\n  Slottable,\n  //\n  Slot as Root,\n};\nexport type { SlotProps };\n"], "names": ["Fragment", "Slot", "props", "Slottable"], "mappings": ";;;;;;;;AAAA,YAAY,WAAW;AACvB,SAAS,mBAAmB;AAmCpB,SAkEG,YAAAA,WAlEH;;;;AAAA,uBAAA;AAzB0B,SAAS,WAAW,SAAA,EAAmB;IACvE,MAAM,YAAY,aAAA,GAAA,gBAAgB,SAAS;IAC3C,MAAMC,0KAAa,aAAA,EAAmC,CAAC,OAAO,iBAAiB;QAC7E,MAAM,EAAE,QAAA,EAAU,GAAG,UAAU,CAAA,GAAI;QACnC,MAAM,8KAAsB,WAAA,CAAS,OAAA,CAAQ,QAAQ;QACrD,MAAM,YAAY,cAAc,IAAA,CAAK,WAAW;QAEhD,IAAI,WAAW;YAEb,MAAM,aAAa,UAAU,KAAA,CAAM,QAAA;YAEnC,MAAM,cAAc,cAAc,GAAA,CAAI,CAAC,UAAU;gBAC/C,IAAI,UAAU,WAAW;oBAGvB,IAAU,yKAAA,CAAS,KAAA,CAAM,UAAU,IAAI,EAAG,CAAA,qKAAa,WAAA,CAAS,IAAA,CAAK,IAAI;oBACzE,WAAa,+KAAA,EAAe,UAAU,IACjC,WAAW,KAAA,CAAwC,QAAA,GACpD;gBACN,OAAO;oBACL,OAAO;gBACT;YACF,CAAC;YAED,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,WAAA;gBAAW,GAAG,SAAA;gBAAW,KAAK;gBAC5B,4KAAM,iBAAA,EAAe,UAAU,QACtB,6KAAA,EAAa,YAAY,KAAA,GAAW,WAAW,IACrD;YAAA,CACN;QAEJ;QAEA,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,WAAA;YAAW,GAAG,SAAA;YAAW,KAAK;YAC5B;QAAA,CACH;IAEJ,CAAC;IAEDA,MAAK,WAAA,GAAc,GAAG,SAAS,CAAA,KAAA,CAAA;IAC/B,OAAOA;AACT;AAEA,IAAM,OAAO,aAAA,GAAA,WAAW,MAAM;AAAA,uBAAA;AAUH,SAAS,gBAAgB,SAAA,EAAmB;IACrE,MAAM,6KAAkB,cAAA,EAAgC,CAAC,OAAO,iBAAiB;QAC/E,MAAM,EAAE,QAAA,EAAU,GAAG,UAAU,CAAA,GAAI;QAEnC,QAAU,+KAAA,EAAe,QAAQ,GAAG;YAClC,MAAM,cAAc,cAAc,QAAQ;YAC1C,MAAMC,SAAQ,WAAW,WAAW,SAAS,KAAiB;YAE9D,IAAI,SAAS,IAAA,mKAAe,WAAA,EAAU;gBACpCA,OAAM,GAAA,GAAM,gBAAe,sPAAA,EAAY,cAAc,WAAW,IAAI;YACtE;YACA,yKAAa,eAAA,EAAa,UAAUA,MAAK;QAC3C;QAEA,qKAAa,WAAA,CAAS,KAAA,CAAM,QAAQ,IAAI,kKAAU,WAAA,CAAS,IAAA,CAAK,IAAI,IAAI;IAC1E,CAAC;IAED,UAAU,WAAA,GAAc,GAAG,SAAS,CAAA,UAAA,CAAA;IACpC,OAAO;AACT;AAMA,IAAM,uBAAuB,OAAO,iBAAiB;AAAA,uBAAA;AAUnB,SAAS,gBAAgB,SAAA,EAAmB;IAC5E,MAAMC,aAAgC,CAAC,EAAE,QAAA,CAAS,CAAA,KAAM;QACtD,OAAO,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,yKAAAH,WAAAA,EAAA;YAAG;QAAA,CAAS;IACrB;IACAG,WAAU,WAAA,GAAc,GAAG,SAAS,CAAA,UAAA,CAAA;IACpCA,WAAU,SAAA,GAAY;IACtB,OAAOA;AACT;AAEA,IAAM,YAAY,aAAA,GAAA,gBAAgB,WAAW;AAM7C,SAAS,YACP,KAAA,EAC+D;IAC/D,QACQ,kLAAA,EAAe,KAAK,KAC1B,OAAO,MAAM,IAAA,KAAS,cACtB,eAAe,MAAM,IAAA,IACrB,MAAM,IAAA,CAAK,SAAA,KAAc;AAE7B;AAEA,SAAS,WAAW,SAAA,EAAqB,UAAA,EAAsB;IAE7D,MAAM,gBAAgB;QAAE,GAAG,UAAA;IAAW;IAEtC,IAAA,MAAW,YAAY,WAAY;QACjC,MAAM,gBAAgB,SAAA,CAAU,QAAQ,CAAA;QACxC,MAAM,iBAAiB,UAAA,CAAW,QAAQ,CAAA;QAE1C,MAAM,YAAY,WAAW,IAAA,CAAK,QAAQ;QAC1C,IAAI,WAAW;YAEb,IAAI,iBAAiB,gBAAgB;gBACnC,aAAA,CAAc,QAAQ,CAAA,GAAI,CAAA,GAAI,SAAoB;oBAChD,eAAe,GAAG,IAAI;oBACtB,cAAc,GAAG,IAAI;gBACvB;YACF,OAAA,IAES,eAAe;gBACtB,aAAA,CAAc,QAAQ,CAAA,GAAI;YAC5B;QACF,OAAA,IAES,aAAa,SAAS;YAC7B,aAAA,CAAc,QAAQ,CAAA,GAAI;gBAAE,GAAG,aAAA;gBAAe,GAAG,cAAA;YAAe;QAClE,OAAA,IAAW,aAAa,aAAa;YACnC,aAAA,CAAc,QAAQ,CAAA,GAAI;gBAAC;gBAAe,cAAc;aAAA,CAAE,MAAA,CAAO,OAAO,EAAE,IAAA,CAAK,GAAG;QACpF;IACF;IAEA,OAAO;QAAE,GAAG,SAAA;QAAW,GAAG,aAAA;IAAc;AAC1C;AAOA,SAAS,cAAc,OAAA,EAA6B;IAElD,IAAI,SAAS,OAAO,wBAAA,CAAyB,QAAQ,KAAA,EAAO,KAAK,GAAG;IACpE,IAAI,UAAU,UAAU,oBAAoB,UAAU,OAAO,cAAA;IAC7D,IAAI,SAAS;QACX,OAAQ,QAAgB,GAAA;IAC1B;IAGA,SAAS,OAAO,wBAAA,CAAyB,SAAS,KAAK,GAAG;IAC1D,UAAU,UAAU,oBAAoB,UAAU,OAAO,cAAA;IACzD,IAAI,SAAS;QACX,OAAQ,QAAQ,KAAA,CAAuC,GAAA;IACzD;IAGA,OAAQ,QAAQ,KAAA,CAAuC,GAAA,IAAQ,QAAgB,GAAA;AACjF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2481, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/node_modules/%40radix-ui/react-checkbox/node_modules/%40radix-ui/react-primitive/src/primitive.tsx"], "sourcesContent": ["import * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport { createSlot } from '@radix-ui/react-slot';\n\nconst NODES = [\n  'a',\n  'button',\n  'div',\n  'form',\n  'h2',\n  'h3',\n  'img',\n  'input',\n  'label',\n  'li',\n  'nav',\n  'ol',\n  'p',\n  'span',\n  'svg',\n  'ul',\n] as const;\n\ntype Primitives = { [E in (typeof NODES)[number]]: PrimitiveForwardRefComponent<E> };\ntype PrimitivePropsWithRef<E extends React.ElementType> = React.ComponentPropsWithRef<E> & {\n  asChild?: boolean;\n};\n\ninterface PrimitiveForwardRefComponent<E extends React.ElementType>\n  extends React.ForwardRefExoticComponent<PrimitivePropsWithRef<E>> {}\n\n/* -------------------------------------------------------------------------------------------------\n * Primitive\n * -----------------------------------------------------------------------------------------------*/\n\nconst Primitive = NODES.reduce((primitive, node) => {\n  const Slot = createSlot(`Primitive.${node}`);\n  const Node = React.forwardRef((props: PrimitivePropsWithRef<typeof node>, forwardedRef: any) => {\n    const { asChild, ...primitiveProps } = props;\n    const Comp: any = asChild ? Slot : node;\n\n    if (typeof window !== 'undefined') {\n      (window as any)[Symbol.for('radix-ui')] = true;\n    }\n\n    return <Comp {...primitiveProps} ref={forwardedRef} />;\n  });\n\n  Node.displayName = `Primitive.${node}`;\n\n  return { ...primitive, [node]: Node };\n}, {} as Primitives);\n\n/* -------------------------------------------------------------------------------------------------\n * Utils\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * Flush custom event dispatch\n * https://github.com/radix-ui/primitives/pull/1378\n *\n * React batches *all* event handlers since version 18, this introduces certain considerations when using custom event types.\n *\n * Internally, React prioritises events in the following order:\n *  - discrete\n *  - continuous\n *  - default\n *\n * https://github.com/facebook/react/blob/a8a4742f1c54493df00da648a3f9d26e3db9c8b5/packages/react-dom/src/events/ReactDOMEventListener.js#L294-L350\n *\n * `discrete` is an  important distinction as updates within these events are applied immediately.\n * React however, is not able to infer the priority of custom event types due to how they are detected internally.\n * Because of this, it's possible for updates from custom events to be unexpectedly batched when\n * dispatched by another `discrete` event.\n *\n * In order to ensure that updates from custom events are applied predictably, we need to manually flush the batch.\n * This utility should be used when dispatching a custom event from within another `discrete` event, this utility\n * is not necessary when dispatching known event types, or if dispatching a custom type inside a non-discrete event.\n * For example:\n *\n * dispatching a known click 👎\n * target.dispatchEvent(new Event(‘click’))\n *\n * dispatching a custom type within a non-discrete event 👎\n * onScroll={(event) => event.target.dispatchEvent(new CustomEvent(‘customType’))}\n *\n * dispatching a custom type within a `discrete` event 👍\n * onPointerDown={(event) => dispatchDiscreteCustomEvent(event.target, new CustomEvent(‘customType’))}\n *\n * Note: though React classifies `focus`, `focusin` and `focusout` events as `discrete`, it's  not recommended to use\n * this utility with them. This is because it's possible for those handlers to be called implicitly during render\n * e.g. when focus is within a component as it is unmounted, or when managing focus on mount.\n */\n\nfunction dispatchDiscreteCustomEvent<E extends CustomEvent>(target: E['target'], event: E) {\n  if (target) ReactDOM.flushSync(() => target.dispatchEvent(event));\n}\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Primitive;\n\nexport {\n  Primitive,\n  //\n  Root,\n  //\n  dispatchDiscreteCustomEvent,\n};\nexport type { PrimitivePropsWithRef };\n"], "names": [], "mappings": ";;;;;;AAAA,YAAY,WAAW;AACvB,YAAY,cAAc;AAC1B,SAAS,kBAAkB;AA2ChB;;;;;AAzCX,IAAM,QAAQ;IACZ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACF;AAcA,IAAM,YAAY,MAAM,MAAA,CAAO,CAAC,WAAW,SAAS;IAClD,MAAM,qOAAO,aAAA,EAAW,CAAA,UAAA,EAAa,IAAI,EAAE;IAC3C,MAAM,yKAAa,aAAA,EAAW,CAAC,OAA2C,iBAAsB;QAC9F,MAAM,EAAE,OAAA,EAAS,GAAG,eAAe,CAAA,GAAI;QACvC,MAAM,OAAY,UAAU,OAAO;QAEnC,IAAI,OAAO,WAAW,aAAa;YAChC,MAAA,CAAe,OAAO,GAAA,CAAI,UAAU,CAAC,CAAA,GAAI;QAC5C;QAEA,OAAO,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,MAAA;YAAM,GAAG,cAAA;YAAgB,KAAK;QAAA,CAAc;IACtD,CAAC;IAED,KAAK,WAAA,GAAc,CAAA,UAAA,EAAa,IAAI,EAAA;IAEpC,OAAO;QAAE,GAAG,SAAA;QAAW,CAAC,IAAI,CAAA,EAAG;IAAK;AACtC,GAAG,CAAC,CAAe;AA2CnB,SAAS,4BAAmD,MAAA,EAAqB,KAAA,EAAU;IACzF,IAAI,OAAQ,0KAAS,YAAA,EAAU,IAAM,OAAO,aAAA,CAAc,KAAK,CAAC;AAClE;AAIA,IAAM,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2544, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/node_modules/%40radix-ui/react-checkbox/src/checkbox.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { usePrevious } from '@radix-ui/react-use-previous';\nimport { useSize } from '@radix-ui/react-use-size';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive } from '@radix-ui/react-primitive';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Checkbox\n * -----------------------------------------------------------------------------------------------*/\n\nconst CHECKBOX_NAME = 'Checkbox';\n\ntype ScopedProps<P> = P & { __scopeCheckbox?: Scope };\nconst [createCheckboxContext, createCheckboxScope] = createContextScope(CHECKBOX_NAME);\n\ntype CheckedState = boolean | 'indeterminate';\n\ntype CheckboxContextValue = {\n  state: CheckedState;\n  disabled?: boolean;\n};\n\nconst [CheckboxProvider, useCheckboxContext] =\n  createCheckboxContext<CheckboxContextValue>(CHECKBOX_NAME);\n\ntype CheckboxElement = React.ElementRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface CheckboxProps extends Omit<PrimitiveButtonProps, 'checked' | 'defaultChecked'> {\n  checked?: CheckedState;\n  defaultChecked?: CheckedState;\n  required?: boolean;\n  onCheckedChange?(checked: CheckedState): void;\n}\n\nconst Checkbox = React.forwardRef<CheckboxElement, CheckboxProps>(\n  (props: ScopedProps<CheckboxProps>, forwardedRef) => {\n    const {\n      __scopeCheckbox,\n      name,\n      checked: checkedProp,\n      defaultChecked,\n      required,\n      disabled,\n      value = 'on',\n      onCheckedChange,\n      form,\n      ...checkboxProps\n    } = props;\n    const [button, setButton] = React.useState<HTMLButtonElement | null>(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setButton(node));\n    const hasConsumerStoppedPropagationRef = React.useRef(false);\n    // We set this to true by default so that events bubble to forms without JS (SSR)\n    const isFormControl = button ? form || !!button.closest('form') : true;\n    const [checked = false, setChecked] = useControllableState({\n      prop: checkedProp,\n      defaultProp: defaultChecked,\n      onChange: onCheckedChange,\n    });\n    const initialCheckedStateRef = React.useRef(checked);\n    React.useEffect(() => {\n      const form = button?.form;\n      if (form) {\n        const reset = () => setChecked(initialCheckedStateRef.current);\n        form.addEventListener('reset', reset);\n        return () => form.removeEventListener('reset', reset);\n      }\n    }, [button, setChecked]);\n\n    return (\n      <CheckboxProvider scope={__scopeCheckbox} state={checked} disabled={disabled}>\n        <Primitive.button\n          type=\"button\"\n          role=\"checkbox\"\n          aria-checked={isIndeterminate(checked) ? 'mixed' : checked}\n          aria-required={required}\n          data-state={getState(checked)}\n          data-disabled={disabled ? '' : undefined}\n          disabled={disabled}\n          value={value}\n          {...checkboxProps}\n          ref={composedRefs}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            // According to WAI ARIA, Checkboxes don't activate on enter keypress\n            if (event.key === 'Enter') event.preventDefault();\n          })}\n          onClick={composeEventHandlers(props.onClick, (event) => {\n            setChecked((prevChecked) => (isIndeterminate(prevChecked) ? true : !prevChecked));\n            if (isFormControl) {\n              hasConsumerStoppedPropagationRef.current = event.isPropagationStopped();\n              // if checkbox is in a form, stop propagation from the button so that we only propagate\n              // one click event (from the input). We propagate changes from an input so that native\n              // form validation works and form events reflect checkbox updates.\n              if (!hasConsumerStoppedPropagationRef.current) event.stopPropagation();\n            }\n          })}\n        />\n        {isFormControl && (\n          <BubbleInput\n            control={button}\n            bubbles={!hasConsumerStoppedPropagationRef.current}\n            name={name}\n            value={value}\n            checked={checked}\n            required={required}\n            disabled={disabled}\n            form={form}\n            // We transform because the input is absolutely positioned but we have\n            // rendered it **after** the button. This pulls it back to sit on top\n            // of the button.\n            style={{ transform: 'translateX(-100%)' }}\n            defaultChecked={isIndeterminate(defaultChecked) ? false : defaultChecked}\n          />\n        )}\n      </CheckboxProvider>\n    );\n  }\n);\n\nCheckbox.displayName = CHECKBOX_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * CheckboxIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst INDICATOR_NAME = 'CheckboxIndicator';\n\ntype CheckboxIndicatorElement = React.ElementRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface CheckboxIndicatorProps extends PrimitiveSpanProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst CheckboxIndicator = React.forwardRef<CheckboxIndicatorElement, CheckboxIndicatorProps>(\n  (props: ScopedProps<CheckboxIndicatorProps>, forwardedRef) => {\n    const { __scopeCheckbox, forceMount, ...indicatorProps } = props;\n    const context = useCheckboxContext(INDICATOR_NAME, __scopeCheckbox);\n    return (\n      <Presence present={forceMount || isIndeterminate(context.state) || context.state === true}>\n        <Primitive.span\n          data-state={getState(context.state)}\n          data-disabled={context.disabled ? '' : undefined}\n          {...indicatorProps}\n          ref={forwardedRef}\n          style={{ pointerEvents: 'none', ...props.style }}\n        />\n      </Presence>\n    );\n  }\n);\n\nCheckboxIndicator.displayName = INDICATOR_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype InputProps = React.ComponentPropsWithoutRef<'input'>;\ninterface BubbleInputProps extends Omit<InputProps, 'checked'> {\n  checked: CheckedState;\n  control: HTMLElement | null;\n  bubbles: boolean;\n}\n\nconst BubbleInput = (props: BubbleInputProps) => {\n  const { control, checked, bubbles = true, defaultChecked, ...inputProps } = props;\n  const ref = React.useRef<HTMLInputElement>(null);\n  const prevChecked = usePrevious(checked);\n  const controlSize = useSize(control);\n\n  // Bubble checked change to parents (e.g form change event)\n  React.useEffect(() => {\n    const input = ref.current!;\n    const inputProto = window.HTMLInputElement.prototype;\n    const descriptor = Object.getOwnPropertyDescriptor(inputProto, 'checked') as PropertyDescriptor;\n    const setChecked = descriptor.set;\n\n    if (prevChecked !== checked && setChecked) {\n      const event = new Event('click', { bubbles });\n      input.indeterminate = isIndeterminate(checked);\n      setChecked.call(input, isIndeterminate(checked) ? false : checked);\n      input.dispatchEvent(event);\n    }\n  }, [prevChecked, checked, bubbles]);\n\n  const defaultCheckedRef = React.useRef(isIndeterminate(checked) ? false : checked);\n  return (\n    <input\n      type=\"checkbox\"\n      aria-hidden\n      defaultChecked={defaultChecked ?? defaultCheckedRef.current}\n      {...inputProps}\n      tabIndex={-1}\n      ref={ref}\n      style={{\n        ...props.style,\n        ...controlSize,\n        position: 'absolute',\n        pointerEvents: 'none',\n        opacity: 0,\n        margin: 0,\n      }}\n    />\n  );\n};\n\nfunction isIndeterminate(checked?: CheckedState): checked is 'indeterminate' {\n  return checked === 'indeterminate';\n}\n\nfunction getState(checked: CheckedState) {\n  return isIndeterminate(checked) ? 'indeterminate' : checked ? 'checked' : 'unchecked';\n}\n\nconst Root = Checkbox;\nconst Indicator = CheckboxIndicator;\n\nexport {\n  createCheckboxScope,\n  //\n  Checkbox,\n  CheckboxIndicator,\n  //\n  Root,\n  Indicator,\n};\nexport type { CheckboxProps, CheckboxIndicatorProps, CheckedState };\n"], "names": ["form"], "mappings": ";;;;;;;;AAAA,YAAY,WAAW;AACvB,SAAS,uBAAuB;AAChC,SAAS,0BAA0B;AACnC,SAAS,4BAA4B;AACrC,SAAS,4BAA4B;AACrC,SAAS,mBAAmB;AAC5B,SAAS,eAAe;AACxB,SAAS,gBAAgB;AACzB,SAAS,iBAAiB;AAmEpB,SACE,KADF;;;;;;;;;;;;AA3DN,IAAM,gBAAgB;AAGtB,IAAM,CAAC,uBAAuB,mBAAmB,CAAA,oOAAI,qBAAA,EAAmB,aAAa;AASrF,IAAM,CAAC,kBAAkB,kBAAkB,CAAA,GACzC,sBAA4C,aAAa;AAW3D,IAAM,6KAAiB,aAAA,EACrB,CAAC,OAAmC,iBAAiB;IACnD,MAAM,EACJ,eAAA,EACA,IAAA,EACA,SAAS,WAAA,EACT,cAAA,EACA,QAAA,EACA,QAAA,EACA,QAAQ,IAAA,EACR,eAAA,EACA,IAAA,EACA,GAAG,eACL,GAAI;IACJ,MAAM,CAAC,QAAQ,SAAS,CAAA,qKAAU,WAAA,EAAmC,IAAI;IACzE,MAAM,uPAAe,mBAAA,EAAgB;kDAAc,CAAC,OAAS,UAAU,IAAI,CAAC;;IAC5E,MAAM,qMAAyC,SAAA,EAAO,KAAK;IAE3D,MAAM,gBAAgB,SAAS,QAAQ,CAAC,CAAC,OAAO,OAAA,CAAQ,MAAM,IAAI;IAClE,MAAM,CAAC,UAAU,KAAA,EAAO,UAAU,CAAA,yPAAI,uBAAA,EAAqB;QACzD,MAAM;QACN,aAAa;QACb,UAAU;IACZ,CAAC;IACD,MAAM,0LAA+B,UAAA,EAAO,OAAO;sKAC7C,YAAA;8BAAU,MAAM;YACpB,MAAMA,QAAO,QAAQ;YACrB,IAAIA,OAAM;gBACR,MAAM;gDAAQ,IAAM,WAAW,uBAAuB,OAAO;;gBAC7DA,MAAK,gBAAA,CAAiB,SAAS,KAAK;gBACpC;0CAAO,IAAMA,MAAK,mBAAA,CAAoB,SAAS,KAAK;;YACtD;QACF;6BAAG;QAAC;QAAQ,UAAU;KAAC;IAEvB,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,OAAA,EAAC,kBAAA;QAAiB,OAAO;QAAiB,OAAO;QAAS;QACxD,UAAA;YAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,iOAAC,YAAA,CAAU,MAAA,EAAV;gBACC,MAAK;gBACL,MAAK;gBACL,gBAAc,gBAAgB,OAAO,IAAI,UAAU;gBACnD,iBAAe;gBACf,cAAY,SAAS,OAAO;gBAC5B,iBAAe,WAAW,KAAK,KAAA;gBAC/B;gBACA;gBACC,GAAG,aAAA;gBACJ,KAAK;gBACL,eAAW,6OAAA,EAAqB,MAAM,SAAA,EAAW,CAAC,UAAU;oBAE1D,IAAI,MAAM,GAAA,KAAQ,QAAS,CAAA,MAAM,cAAA,CAAe;gBAClD,CAAC;gBACD,aAAS,6OAAA,EAAqB,MAAM,OAAA,EAAS,CAAC,UAAU;oBACtD,WAAW,CAAC,cAAiB,gBAAgB,WAAW,IAAI,OAAO,CAAC,WAAY;oBAChF,IAAI,eAAe;wBACjB,iCAAiC,OAAA,GAAU,MAAM,oBAAA,CAAqB;wBAItE,IAAI,CAAC,iCAAiC,OAAA,CAAS,CAAA,MAAM,eAAA,CAAgB;oBACvE;gBACF,CAAC;YAAA;YAEF,iBACC,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,aAAA;gBACC,SAAS;gBACT,SAAS,CAAC,iCAAiC,OAAA;gBAC3C;gBACA;gBACA;gBACA;gBACA;gBACA;gBAIA,OAAO;oBAAE,WAAW;gBAAoB;gBACxC,gBAAgB,gBAAgB,cAAc,IAAI,QAAQ;YAAA;SAC5D;IAAA,CAEJ;AAEJ;AAGF,SAAS,WAAA,GAAc;AAMvB,IAAM,iBAAiB;AAYvB,IAAM,sLAA0B,aAAA,EAC9B,CAAC,OAA4C,iBAAiB;IAC5D,MAAM,EAAE,eAAA,EAAiB,UAAA,EAAY,GAAG,eAAe,CAAA,GAAI;IAC3D,MAAM,UAAU,mBAAmB,gBAAgB,eAAe;IAClE,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,gOAAC,WAAA,EAAA;QAAS,SAAS,cAAc,gBAAgB,QAAQ,KAAK,KAAK,QAAQ,KAAA,KAAU;QACnF,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,iOAAC,YAAA,CAAU,IAAA,EAAV;YACC,cAAY,SAAS,QAAQ,KAAK;YAClC,iBAAe,QAAQ,QAAA,GAAW,KAAK,KAAA;YACtC,GAAG,cAAA;YACJ,KAAK;YACL,OAAO;gBAAE,eAAe;gBAAQ,GAAG,MAAM,KAAA;YAAM;QAAA;IACjD,CACF;AAEJ;AAGF,kBAAkB,WAAA,GAAc;AAWhC,IAAM,cAAc,CAAC,UAA4B;IAC/C,MAAM,EAAE,OAAA,EAAS,OAAA,EAAS,UAAU,IAAA,EAAM,cAAA,EAAgB,GAAG,WAAW,CAAA,GAAI;IAC5E,MAAM,MAAY,2KAAA,EAAyB,IAAI;IAC/C,MAAM,uPAAc,cAAA,EAAY,OAAO;IACvC,MAAM,eAAc,8OAAA,EAAQ,OAAO;sKAG7B,YAAA;iCAAU,MAAM;YACpB,MAAM,QAAQ,IAAI,OAAA;YAClB,MAAM,aAAa,OAAO,gBAAA,CAAiB,SAAA;YAC3C,MAAM,aAAa,OAAO,wBAAA,CAAyB,YAAY,SAAS;YACxE,MAAM,aAAa,WAAW,GAAA;YAE9B,IAAI,gBAAgB,WAAW,YAAY;gBACzC,MAAM,QAAQ,IAAI,MAAM,SAAS;oBAAE;gBAAQ,CAAC;gBAC5C,MAAM,aAAA,GAAgB,gBAAgB,OAAO;gBAC7C,WAAW,IAAA,CAAK,OAAO,gBAAgB,OAAO,IAAI,QAAQ,OAAO;gBACjE,MAAM,aAAA,CAAc,KAAK;YAC3B;QACF;gCAAG;QAAC;QAAa;QAAS,OAAO;KAAC;IAElC,MAAM,sLAA0B,SAAA,EAAO,gBAAgB,OAAO,IAAI,QAAQ,OAAO;IACjF,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,SAAA;QACC,MAAK;QACL,eAAW;QACX,gBAAgB,kBAAkB,kBAAkB,OAAA;QACnD,GAAG,UAAA;QACJ,UAAU,CAAA;QACV;QACA,OAAO;YACL,GAAG,MAAM,KAAA;YACT,GAAG,WAAA;YACH,UAAU;YACV,eAAe;YACf,SAAS;YACT,QAAQ;QACV;IAAA;AAGN;AAEA,SAAS,gBAAgB,OAAA,EAAoD;IAC3E,OAAO,YAAY;AACrB;AAEA,SAAS,SAAS,OAAA,EAAuB;IACvC,OAAO,gBAAgB,OAAO,IAAI,kBAAkB,UAAU,YAAY;AAC5E;AAEA,IAAM,OAAO;AACb,IAAM,YAAY", "ignoreList": [0], "debugId": null}}]}