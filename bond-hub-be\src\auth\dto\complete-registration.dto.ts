import {
  IsString,
  <PERSON><PERSON>otEmpt<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  IsE<PERSON>,
  IsDateString,
} from 'class-validator';
import { Gender } from '@prisma/client';

export class CompleteRegistrationDto {
  @IsString()
  @IsNotEmpty()
  registrationId: string;

  @IsString()
  @IsNotEmpty()
  @MinLength(6)
  password: string;

  @IsString()
  @IsNotEmpty()
  fullName: string;

  @IsDateString()
  @IsNotEmpty()
  dateOfBirth: string;

  @IsEnum(Gender)
  @IsNotEmpty()
  gender: Gender;
}
