{"info": {"_postman_id": "a5e7b8c9-d0e1-4f23-a456-789b0c1d2e3f", "name": "Vodka <PERSON> <PERSON><PERSON><PERSON>ử <PERSON> 12/4", "description": "<PERSON><PERSON> kiểm thử API cho các chức năng tin nhắn trong ứng dụng BondHub", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "apiUrl", "value": "http://localhost:3000", "type": "string", "description": "URL cơ sở của API"}, {"key": "accessToken", "value": "", "type": "string", "description": "Token xác thực ng<PERSON>ời dùng"}, {"key": "senderId", "value": "", "type": "string", "description": "ID của người gửi tin nh<PERSON>n"}, {"key": "receiverId", "value": "", "type": "string", "description": "ID của người nhận tin nhắn"}, {"key": "groupId", "value": "", "type": "string", "description": "ID của nhóm"}, {"key": "messageId", "value": "", "type": "string", "description": "<PERSON> của tin nh<PERSON>n"}], "item": [{"name": "<PERSON><PERSON><PERSON> th<PERSON>c", "item": [{"name": "<PERSON><PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["var jsonData = pm.response.json();", "", "if (jsonData && jsonData.accessToken) {", "    pm.collectionVariables.set(\"accessToken\", jsonData.accessToken);", "    console.log(\"<PERSON><PERSON> lưu token xác thực\");", "}", "", "if (jsonData && jsonData.user && jsonData.user.id) {", "    pm.collectionVariables.set(\"senderId\", jsonData.user.id);", "    console.log(\"<PERSON><PERSON> lưu ID người dùng\");", "}", "", "pm.test(\"<PERSON><PERSON><PERSON> nhập thành công\", function () {", "    pm.response.to.have.status(200);", "    pm.expect(jsonData).to.have.property('accessToken');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\",\n    \"deviceType\": \"WEB\"\n}"}, "url": {"raw": "{{apiUrl}}/auth/login", "host": ["{{apiUrl}}"], "path": ["auth", "login"]}}}]}, {"name": "<PERSON><PERSON><PERSON><PERSON> trò ch<PERSON>", "item": [{"name": "<PERSON><PERSON><PERSON> danh s<PERSON>ch cuộc trò chuy<PERSON>n", "event": [{"listen": "test", "script": {"exec": ["var jsonData = pm.response.json();", "", "pm.test(\"<PERSON><PERSON><PERSON> hồi có cấu trúc đúng\", function () {", "    pm.response.to.have.status(200);", "    pm.expect(jsonData).to.have.property('conversations');", "    pm.expect(jsonData).to.have.property('totalCount');", "    pm.expect(jsonData.conversations).to.be.an('array');", "});", "", "// <PERSON><PERSON><PERSON> tra chi tiết cuộc trò chuyện nếu có", "if (jsonData.conversations && jsonData.conversations.length > 0) {", "    var firstConversation = jsonData.conversations[0];", "    ", "    pm.test(\"<PERSON><PERSON><PERSON><PERSON> trò chuyện có cấu trúc đúng\", function () {", "        pm.expect(firstConversation).to.have.property('id');", "        pm.expect(firstConversation).to.have.property('type');", "        pm.expect(['USER', 'GROUP']).to.include(firstConversation.type);", "        pm.expect(firstConversation).to.have.property('unreadCount');", "        pm.expect(firstConversation).to.have.property('updatedAt');", "        ", "        // <PERSON><PERSON><PERSON> tra thuộc tính theo loại cuộc trò chuy<PERSON>n", "        if (firstConversation.type === 'USER') {", "            pm.expect(firstConversation).to.have.property('user');", "            pm.expect(firstConversation.user).to.have.property('id');", "            pm.expect(firstConversation.user).to.have.property('fullName');", "        } else if (firstConversation.type === 'GROUP') {", "            pm.expect(firstConversation).to.have.property('group');", "            pm.expect(firstConversation.group).to.have.property('id');", "            pm.expect(firstConversation.group).to.have.property('name');", "        }", "        ", "        // <PERSON><PERSON><PERSON> tra tin nhắn cuối cùng nếu có", "        if (firstConversation.lastMessage) {", "            pm.expect(firstConversation.lastMessage).to.have.property('id');", "            pm.expect(firstConversation.lastMessage).to.have.property('content');", "            pm.expect(firstConversation.lastMessage).to.have.property('senderId');", "            pm.expect(firstConversation.lastMessage).to.have.property('createdAt');", "        }", "    });", "    ", "    // <PERSON><PERSON><PERSON> cuộc trò chuyện đầu tiên để sử dụng trong các test khác", "    if (firstConversation.type === 'USER') {", "        pm.collectionVariables.set(\"receiverId\", firstConversation.user.id);", "        console.log(\"<PERSON><PERSON> lưu ID người nhận từ cuộc trò chuyện đầu tiên\");", "    } else if (firstConversation.type === 'GROUP') {", "        pm.collectionVariables.set(\"groupId\", firstConversation.group.id);", "        console.log(\"<PERSON><PERSON> lưu ID nhóm từ cuộc trò chuyện đầu tiên\");", "    }", "}", "", "// <PERSON><PERSON><PERSON> tra phân trang", "var url = pm.request.url.toString();", "var pageMatch = url.match(/page=(\\d+)/);", "var limitMatch = url.match(/limit=(\\d+)/);", "var page = pageMatch ? parseInt(pageMatch[1]) : 1;", "var limit = limitMatch ? parseInt(limitMatch[1]) : 20;", "", "pm.test(\"Phân trang hoạt động đúng\", function () {", "    if (jsonData.conversations.length < limit) {", "        pm.expect(jsonData.conversations.length).to.be.at.most(limit);", "    } else {", "        pm.expect(jsonData.conversations.length).to.equal(limit);", "    }", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{apiUrl}}/messages/conversations?page=1&limit=20", "host": ["{{apiUrl}}"], "path": ["messages", "conversations"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}, "description": "<PERSON><PERSON><PERSON> danh sách cuộc trò chuyện của người dùng hiện tại, bao gồm cả trò chuyện cá nhân và nhóm"}}]}, {"name": "Tin nhắn cá nhân", "item": [{"name": "<PERSON><PERSON><PERSON> tin nhắn với người dùng", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{apiUrl}}/messages/user/{{receiverId}}?page=1", "host": ["{{apiUrl}}"], "path": ["messages", "user", "{{receiverId}}"], "query": [{"key": "page", "value": "1"}]}}}, {"name": "<PERSON><PERSON><PERSON> kiếm tin nhắn với người dùng", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{apiUrl}}/messages/user/{{receiverId}}/search?searchText=hello&page=1", "host": ["{{apiUrl}}"], "path": ["messages", "user", "{{receiverId}}", "search"], "query": [{"key": "searchText", "value": "hello"}, {"key": "page", "value": "1"}]}}}, {"name": "<PERSON><PERSON><PERSON> tin nhắn văn bản", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "body": {"mode": "raw", "raw": "{\n    \"receiverId\": \"{{receiverId}}\",\n    \"content\": {\n        \"text\": \"Xin chào!\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{apiUrl}}/messages/user", "host": ["{{apiUrl}}"], "path": ["messages", "user"]}}}, {"name": "<PERSON><PERSON><PERSON> tin nhắn có file", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "body": {"mode": "formdata", "formdata": [{"key": "receiverId", "value": "{{receiverId}}", "type": "text"}, {"key": "content[text]", "value": "<PERSON><PERSON><PERSON> file đ<PERSON>h k<PERSON>m", "type": "text"}, {"key": "files", "type": "file", "src": []}]}, "url": {"raw": "{{apiUrl}}/messages/user", "host": ["{{apiUrl}}"], "path": ["messages", "user"]}}}]}, {"name": "Tin nhắ<PERSON> nhóm", "item": [{"name": "<PERSON><PERSON><PERSON> tin nhắn nhóm", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{apiUrl}}/messages/group/{{groupId}}?page=1", "host": ["{{apiUrl}}"], "path": ["messages", "group", "{{groupId}}"], "query": [{"key": "page", "value": "1"}]}}}, {"name": "<PERSON><PERSON><PERSON> kiếm tin nhắn trong nhóm", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{apiUrl}}/messages/group/{{groupId}}/search?searchText=hello&page=1", "host": ["{{apiUrl}}"], "path": ["messages", "group", "{{groupId}}", "search"], "query": [{"key": "searchText", "value": "hello"}, {"key": "page", "value": "1"}]}}}, {"name": "<PERSON><PERSON><PERSON> tin nhắn nhóm", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "body": {"mode": "raw", "raw": "{\n    \"groupId\": \"{{groupId}}\",\n    \"content\": {\n        \"text\": \"Xin chào nhóm!\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{apiUrl}}/messages/group", "host": ["{{apiUrl}}"], "path": ["messages", "group"]}}}]}, {"name": "<PERSON><PERSON><PERSON><PERSON> tác tin nh<PERSON>n", "item": [{"name": "<PERSON>hu hồi tin nh<PERSON>n", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{apiUrl}}/messages/recall/{{messageId}}", "host": ["{{apiUrl}}"], "path": ["messages", "recall", "{{messageId}}"]}}}, {"name": "<PERSON><PERSON><PERSON> (phía người dùng)", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{apiUrl}}/messages/deleted-self-side/{{messageId}}", "host": ["{{apiUrl}}"], "path": ["messages", "deleted-self-side", "{{messageId}}"]}}}, {"name": "<PERSON><PERSON><PERSON> dấu đã đọc", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{apiUrl}}/messages/read/{{messageId}}", "host": ["{{apiUrl}}"], "path": ["messages", "read", "{{messageId}}"]}}}, {"name": "<PERSON><PERSON><PERSON> d<PERSON>u ch<PERSON> đ<PERSON>", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{apiUrl}}/messages/unread/{{messageId}}", "host": ["{{apiUrl}}"], "path": ["messages", "unread", "{{messageId}}"]}}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "body": {"mode": "raw", "raw": "{\n    \"messageId\": \"{{messageId}}\",\n    \"reaction\": \"👍\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{apiUrl}}/messages/reaction", "host": ["{{apiUrl}}"], "path": ["messages", "reaction"]}}}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{apiUrl}}/messages/reaction/{{messageId}}", "host": ["{{apiUrl}}"], "path": ["messages", "reaction", "{{messageId}}"]}}}, {"name": "<PERSON><PERSON><PERSON><PERSON> tiếp tin nh<PERSON>n", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "body": {"mode": "raw", "raw": "{\n    \"messageId\": \"{{messageId}}\",\n    \"recipients\": [\n        {\n            \"type\": \"USER\",\n            \"id\": \"{{receiverId}}\"\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{apiUrl}}/messages/forward", "host": ["{{apiUrl}}"], "path": ["messages", "forward"]}}}]}, {"name": "Tải lên media", "item": [{"name": "T<PERSON>i lên file media", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "body": {"mode": "formdata", "formdata": [{"key": "files", "type": "file", "src": []}, {"key": "mediaType", "value": "IMAGE", "type": "text"}, {"key": "receiverId", "value": "{{receiverId}}", "type": "text"}]}, "url": {"raw": "{{apiUrl}}/messages/media", "host": ["{{apiUrl}}"], "path": ["messages", "media"]}}}]}]}