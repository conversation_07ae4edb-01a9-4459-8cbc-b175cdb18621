services:
  api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: vodka
    ports:
      - '3000:3000'
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    env_file:
      - .env
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB}?schema=public
      - PORT=3000
      - REDIS_HOST=${REDIS_HOST}
      - REDIS_PORT=${REDIS_PORT}
    restart: unless-stopped
    networks:
      - vodka-network
    command: >
      sh -c "until nc -z postgres 5432; do
        echo 'Waiting for PostgreSQL to be ready...';
        sleep 2;
      done;
      until nc -z redis 6379; do
        echo 'Waiting for Redis to be ready...';
        sleep 2;
      done;
      echo 'PostgreSQL and Redis are ready!';
      npm run db:migrate &&
      npm run start:prod"

  postgres:
    image: postgres:17
    container_name: postgres
    ports:
      - '5432'
    environment:
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
    volumes:
      - postgres-data:/var/lib/postgresql/data
    networks:
      - vodka-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  redis:
    image: redis:7
    container_name: redis
    ports:
      - '6379:6379'
    volumes:
      - redis-data:/data
    networks:
      - vodka-network
    healthcheck:
      test: ['CMD', 'redis-cli', 'ping']
      interval: 10s
      timeout: 5s
      retries: 5

networks:
  vodka-network:
    driver: bridge

volumes:
  postgres-data:
    name: vodka-postgres-data
  redis-data:
    name: vodka-redis-data