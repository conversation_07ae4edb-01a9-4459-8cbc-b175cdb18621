{"name": "bond-hub-mobile", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "cross-env NODE_ENV=development DARK_MODE=media expo start --android", "ios": "cross-env NODE_ENV=development DARK_MODE=media expo start --ios", "web": "cross-env NODE_ENV=development DARK_MODE=media expo start --web", "test": "jest --watchAll", "lint": "expo lint", "prepare": "husky", "build": "cross-env NODE_ENV=production expo build"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@config-plugins/react-native-webrtc": "^10.0.0", "@expo/html-elements": "^0.4.2", "@expo/vector-icons": "^14.1.0", "@gluestack-ui/actionsheet": "^0.2.46", "@gluestack-ui/alert": "^0.1.16", "@gluestack-ui/alert-dialog": "^0.1.38", "@gluestack-ui/avatar": "^0.1.18", "@gluestack-ui/button": "^1.0.8", "@gluestack-ui/checkbox": "^0.1.33", "@gluestack-ui/fab": "^0.1.22", "@gluestack-ui/form-control": "^0.1.19", "@gluestack-ui/icon": "^0.1.25", "@gluestack-ui/input": "^0.1.32", "@gluestack-ui/menu": "^0.2.43", "@gluestack-ui/nativewind-utils": "^1.0.26", "@gluestack-ui/overlay": "^0.1.16", "@gluestack-ui/pressable": "^0.1.23", "@gluestack-ui/select": "^0.1.30", "@gluestack-ui/toast": "^1.0.9", "@legendapp/motion": "^2.4.0", "@react-native-community/datetimepicker": "8.3.0", "@react-native-community/netinfo": "^11.4.1", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@rneui/themed": "^4.0.0-rc.8", "axios": "^1.7.9", "babel-plugin-module-resolver": "^5.0.2", "clsx": "^2.1.1", "expo": "53.0.9", "expo-av": "~15.1.4", "expo-background-fetch": "~13.1.5", "expo-blur": "~14.1.4", "expo-camera": "~16.1.6", "expo-constants": "~17.1.5", "expo-contacts": "~14.2.4", "expo-dev-client": "~5.1.8", "expo-device": "~7.1.4", "expo-document-picker": "~13.1.5", "expo-file-system": "~18.1.9", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.1.7", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.4", "expo-linking": "~7.1.5", "expo-network": "~7.1.5", "expo-notifications": "~0.31.2", "expo-router": "~5.0.7", "expo-secure-store": "~14.2.3", "expo-sharing": "~13.1.5", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.7", "expo-task-manager": "~13.1.5", "expo-video": "~2.1.9", "expo-video-thumbnails": "~9.1.3", "expo-web-browser": "~14.1.6", "lodash": "^4.17.21", "lucide-react-native": "^0.469.0", "metro-config": "^0.82.3", "nativewind": "^4.1.23", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-css-interop": "^0.1.22", "react-native-date-picker": "^5.0.9", "react-native-emoji-selector": "^0.2.0", "react-native-gesture-handler": "~2.24.0", "react-native-image-viewing": "^0.2.2", "react-native-modal-datetime-picker": "^18.0.0", "react-native-qrcode-svg": "^6.3.15", "react-native-reanimated": "~3.17.4", "react-native-restart": "^0.0.27", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.11.2", "react-native-toast-message": "^2.3.0", "react-native-uuid": "^2.0.3", "react-native-web": "^0.20.0", "react-native-webrtc": "^124.0.5", "react-native-webview": "13.13.5", "rn-emoji-keyboard": "^1.7.0", "socket.io-client": "^4.8.1", "tailwindcss": "^3.4.17", "uuid": "^11.1.0", "zustand": "^5.0.3"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/jest": "^29.5.12", "@types/lodash": "^4.17.16", "@types/react": "~19.0.10", "@types/react-test-renderer": "^18.3.0", "cross-env": "^7.0.3", "husky": "^9.1.7", "jest": "^29.2.1", "jest-expo": "~53.0.5", "jscodeshift": "^0.15.2", "lint-staged": "^15.3.0", "prettier": "3.4.2", "react-native-svg-transformer": "^1.5.0", "react-test-renderer": "18.3.1", "typescript": "~5.8.3"}, "lint-staged": {"**/*": "prettier --write --ignore-unknown"}, "private": true, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e", "expo": {"doctor": {"reactNativeDirectoryCheck": {"exclude": ["expo-barcode-scanner", "react-native-image-viewing", "react-native-emoji-selector", "react-native-restart", "@rneui/themed"], "listUnknownPackages": false}}}}