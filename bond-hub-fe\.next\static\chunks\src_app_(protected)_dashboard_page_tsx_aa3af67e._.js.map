{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/app/%28protected%29/dashboard/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect, useState } from \"react\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { useAuthStore } from \"@/stores/authStore\";\r\n\r\nexport default function Page() {\r\n  const router = useRouter();\r\n  const [redirectAttempted, setRedirectAttempted] = useState(false);\r\n  const { isAuthenticated } = useAuthStore();\r\n\r\n  useEffect(() => {\r\n    // Chỉ chuyển hướng một lần để tránh vòng lặp và chỉ khi đã đăng nhập\r\n    if (!redirectAttempted && isAuthenticated) {\r\n      setRedirectAttempted(true);\r\n      console.log(\"Redirecting from dashboard to chat\");\r\n      // Sử dụng setTimeout để tránh vòng lặp chuyển hướng\r\n      setTimeout(() => {\r\n        router.push(\"/dashboard/chat\", { scroll: false });\r\n      }, 500);\r\n    }\r\n  }, [router, redirectAttempted, isAuthenticated]);\r\n\r\n  // Hi<PERSON>n thị màn hình loading trong khi chuyển hướng\r\n  return (\r\n    <div className=\"flex items-center justify-center min-h-screen\">\r\n      <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500\"></div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,qEAAqE;YACrE,IAAI,CAAC,qBAAqB,iBAAiB;gBACzC,qBAAqB;gBACrB,QAAQ,GAAG,CAAC;gBACZ,oDAAoD;gBACpD;sCAAW;wBACT,OAAO,IAAI,CAAC,mBAAmB;4BAAE,QAAQ;wBAAM;oBACjD;qCAAG;YACL;QACF;yBAAG;QAAC;QAAQ;QAAmB;KAAgB;IAE/C,mDAAmD;IACnD,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;;;;;;;;;;AAGrB;GAvBwB;;QACP,qIAAA,CAAA,YAAS;QAEI,6HAAA,CAAA,eAAY;;;KAHlB", "debugId": null}}]}