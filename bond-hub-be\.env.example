# Environment variables declared in this file are automatically made available to Prisma.
# See the documentation for more detail: https://pris.ly/d/prisma-schema#accessing-environment-variables-from-the-schema

# # Prisma supports the native connection string format for PostgreSQL, MySQL, SQLite, SQL Server, MongoDB and CockroachDB.
# # See the documentation for all the connection string options: https://pris.ly/d/connection-strings

# # Database
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password
POSTGRES_DB=vodka
DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@localhost:5432/${POSTGRES_DB}?schema=public

# # Redis
REDIS_HOST=localhost
REDIS_PORT=6379

# # Environment
NODE_ENV=development
JWT_SECRET=your_jwt_secret

# Email Configuration
EMAIL_TEST_MODE=true
RESEND_API_KEY=your_resend_api_key

# Storage Configuration
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_KEY=your_supabase_service_key

AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_REGION=your_aws_region
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
SMS_TEST_MODE=true

# Mediasoup Configuration
MEDIASOUP_ANNOUNCED_IP=127.0.0.1

GOOGLE_AI_API_KEY=your_gemini_api_key