{"name": "vodka", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "node dist/main", "dev": "nest start --watch", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "prepare": "husky install", "seed": "prisma db seed", "db:migrate": "prisma migrate deploy", "db:seed": "prisma db seed", "db:setup": "npm run db:migrate"}, "dependencies": {"@ai-sdk/google": "^1.2.17", "@aws-sdk/client-sns": "^3.535.0", "@google/generative-ai": "^0.24.1", "@nestjs/cache-manager": "^3.0.0", "@nestjs/common": "^11.0.9", "@nestjs/config": "^4.0.0", "@nestjs/core": "^11.0.9", "@nestjs/event-emitter": "^3.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/mapped-types": "*", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.12", "@nestjs/platform-socket.io": "^11.0.9", "@nestjs/schedule": "^5.0.1", "@nestjs/swagger": "^11.0.3", "@nestjs/websockets": "^11.0.11", "@prisma/client": "^6.2.1", "@redis/client": "^1.6.0", "@supabase/supabase-js": "^2.49.1", "@types/multer": "^1.4.12", "@types/sharp": "^0.31.1", "@types/socket.io": "^3.0.1", "@types/twilio": "^3.19.2", "ai": "^4.3.15", "axios": "^1.8.3", "bcrypt": "^5.1.1", "cache-manager": "^6.4.1", "chalk": "^4.1.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "inquirer": "^8.2.6", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "redis": "^4.7.0", "reflect-metadata": "^0.2.2", "resend": "^4.1.2", "rxjs": "^7.8.1", "sharp": "^0.33.5", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "twilio": "^5.5.1", "uuid": "^11.1.0"}, "devDependencies": {"@nestjs/cli": "^11.0.2", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.9", "@types/bcrypt": "^5.0.2", "@types/express": "^5.0.1", "@types/jest": "^29.5.2", "@types/node": "^22.13.4", "@types/passport-jwt": "^4.0.1", "@types/supertest": "^6.0.0", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.24.0", "@typescript-eslint/parser": "^8.24.0", "eslint": "^8.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "husky": "^8.0.0", "jest": "^29.5.0", "lint-staged": "^15.3.0", "prettier": "^3.4.2", "prisma": "^6.2.1", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "lint-staged": {"*.ts": ["prettier --write", "npm run lint"]}, "prisma": {"seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/seed.ts"}}