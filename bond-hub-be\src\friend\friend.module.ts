import { Modu<PERSON> } from '@nestjs/common';
import { FriendController } from './friend.controller';
import { FriendService } from './friend.service';
import { PrismaModule } from 'src/prisma/prisma.module';
import { FriendGateway } from './friend.gateway';
import { JwtModule } from '@nestjs/jwt';

@Module({
  imports: [
    PrismaModule,
    JwtModule.register({
      secret: process.env.JWT_SECRET,
      signOptions: { expiresIn: '15m' },
    }),
  ],
  controllers: [FriendController],
  providers: [FriendService, FriendGateway],
  exports: [FriendService, FriendGateway],
})
export class FriendModule {}
