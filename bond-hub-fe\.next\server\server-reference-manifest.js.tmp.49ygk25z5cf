self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"786a5cddf431ffb57c7e3310435586f370d34355f2\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/page\": \"action-browser\",\n        \"app/page\": \"action-browser\"\n      }\n    },\n    \"0025527522b7882fd826ad1ca0dce0d0ce6726c702\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/page\": \"action-browser\",\n        \"app/page\": \"action-browser\"\n      }\n    },\n    \"40765b7ae1092c3a68fcf914347f0b297373fe950e\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/page\": \"action-browser\",\n        \"app/page\": \"action-browser\"\n      }\n    },\n    \"406f88a7e8a3e3d4c6790948f974957b18aa302d3c\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/page\": \"action-browser\",\n        \"app/page\": \"action-browser\"\n      }\n    },\n    \"40224cec15feb7e5af4da2d3672710db70fdde3b38\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/page\": \"action-browser\",\n        \"app/page\": \"action-browser\"\n      }\n    },\n    \"608b8e3c5e2d1e07725287aa26bf15eff03cd8a719\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/page\": \"action-browser\",\n        \"app/page\": \"action-browser\"\n      }\n    },\n    \"602666daee370c4b7cd541253a6c5b3eda846249eb\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/page\": \"action-browser\",\n        \"app/page\": \"action-browser\"\n      }\n    },\n    \"60db05fee2171b5299bcb41d3928b3ff3198ff681b\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/page\": \"action-browser\",\n        \"app/page\": \"action-browser\"\n      }\n    },\n    \"60e3d50afefa1bcc6a6ce634bce273f4012a1585b6\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/page\": \"action-browser\",\n        \"app/page\": \"action-browser\"\n      }\n    },\n    \"607be4a979876d1de147e5859be475d19de660820e\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/page\": \"action-browser\",\n        \"app/page\": \"action-browser\"\n      }\n    },\n    \"40f7015ada29a7b632b989c001759c1929ea1b50f7\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/page\": \"action-browser\",\n        \"app/page\": \"action-browser\"\n      }\n    },\n    \"40e73854e278c980b5cec96ba96518bf359593fae1\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/page\": \"action-browser\",\n        \"app/page\": \"action-browser\"\n      }\n    },\n    \"606e93584058962effb204a2e915343a3ed5c58fb4\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/page\": \"action-browser\",\n        \"app/page\": \"action-browser\"\n      }\n    },\n    \"7cd3a5e00c5aa3d7589035e8aa10387789473d26e0\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/page\": \"action-browser\",\n        \"app/page\": \"action-browser\"\n      }\n    },\n    \"00d41e3c118c2c91abbbf555473d2cc2477f0f3f28\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/page\": \"action-browser\",\n        \"app/page\": \"action-browser\"\n      }\n    },\n    \"4067db1247ecf13e53e76ec48d1280e67e62705ece\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/page\": \"action-browser\",\n        \"app/page\": \"action-browser\"\n      }\n    },\n    \"6010c31b664c9e4f4ed8e68a84ae00fb9d01e4af9b\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/page\": \"action-browser\",\n        \"app/page\": \"action-browser\"\n      }\n    },\n    \"6077e13672133007b3a4e8c9c00b04c9dc9718a30c\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/page\": \"action-browser\",\n        \"app/page\": \"action-browser\"\n      }\n    },\n    \"70a829aa97efcb1ce5d25cc2ab07599c6328b4104f\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/page\": \"action-browser\",\n        \"app/page\": \"action-browser\"\n      }\n    },\n    \"60f5648f98cb3e7abcea16c8fb2dfdbed59b782834\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/page\": \"action-browser\",\n        \"app/page\": \"action-browser\"\n      }\n    },\n    \"60dd5c8b1daa63a4e0e736658d190e3917741a5c3d\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/page\": \"action-browser\"\n      }\n    },\n    \"7095df0f39808bb81a7443e68a7f2492274f7660b0\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/page\": \"action-browser\"\n      }\n    },\n    \"604dd71660fae54a3306cda00bc2ac0c2ee23d2141\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/page\": \"action-browser\"\n      }\n    },\n    \"40b0a20f4d004bba2947dda603ee685018f4a81036\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/page\": \"action-browser\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"uuwoIoLbkvZX5tVONZTO2R2Zb/pqsHIjl2GB3+HUeBY=\"\n}"