{"name": "my-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -p 3001", "build": "next build", "start": "next start", "lint": "next lint", "prepare": "husky"}, "dependencies": {"@hookform/resolvers": "^4.1.3", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@types/uuid": "^10.0.0", "axios": "^1.7.9", "cheerio": "^1.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^4.1.0", "emoji-picker-react": "^4.12.2", "framer-motion": "^12.6.3", "input-otp": "^1.4.2", "lucide-react": "^0.484.0", "mediasoup-client": "^3.10.1", "next": "^15.2.4", "next-themes": "^0.4.4", "qrcode.react": "^4.2.0", "react": "^19.0.0", "react-audio-visualize": "^1.2.0", "react-day-picker": "^8.10.1", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "socket.io-client": "^4.8.1", "sonner": "^2.0.3", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "ua-parser-js": "^2.0.3", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/cheerio": "^0.22.35", "@types/node": "^22.13.14", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "^15.2.4", "husky": "^9.1.7", "postcss": "^8", "prettier": "^3.5.3", "tailwindcss": "^3.4.1", "typescript": "^5"}, "lint-staged": {"**/*": "prettier --write --ignore-unknown"}}