[{"name": "hot-reloader", "duration": 240, "timestamp": 817210651, "id": 3, "tags": {"version": "15.3.0"}, "startTime": 1748249267412, "traceId": "2c4657cd13461f7c"}, {"name": "setup-dev-bundler", "duration": 1264954, "timestamp": 816732632, "id": 2, "parentId": 1, "tags": {}, "startTime": 1748249266934, "traceId": "2c4657cd13461f7c"}, {"name": "run-instrumentation-hook", "duration": 77, "timestamp": 818224120, "id": 4, "parentId": 1, "tags": {}, "startTime": 1748249268425, "traceId": "2c4657cd13461f7c"}, {"name": "start-dev-server", "duration": 3154566, "timestamp": 815127703, "id": 1, "tags": {"cpus": "4", "platform": "win32", "memory.freeMem": "7989788672", "memory.totalMem": "17052332032", "memory.heapSizeLimit": "8576303104", "memory.rss": "174878720", "memory.heapTotal": "98983936", "memory.heapUsed": "74854040"}, "startTime": 1748249265329, "traceId": "2c4657cd13461f7c"}, {"name": "ensure-page", "duration": 20096368, "timestamp": 821613014, "id": 6, "parentId": 3, "tags": {"inputPage": "/page"}, "startTime": 1748249271814, "traceId": "2c4657cd13461f7c"}, {"name": "ensure-page", "duration": 145883, "timestamp": 841724888, "id": 10, "parentId": 3, "tags": {"inputPage": "/page"}, "startTime": 1748249291926, "traceId": "2c4657cd13461f7c"}, {"name": "handle-request", "duration": 24009245, "timestamp": 821592732, "id": 5, "tags": {"url": "/"}, "startTime": 1748249271794, "traceId": "2c4657cd13461f7c"}, {"name": "memory-usage", "duration": 28, "timestamp": 845602306, "id": 11, "parentId": 5, "tags": {"url": "/", "memory.rss": "781271040", "memory.heapUsed": "104598232", "memory.heapTotal": "143818752"}, "startTime": 1748249295804, "traceId": "2c4657cd13461f7c"}, {"name": "compile-path", "duration": 24460509, "timestamp": 821614054, "id": 7, "tags": {"trigger": "/"}, "startTime": 1748249271815, "traceId": "2c4657cd13461f7c"}, {"name": "ensure-page", "duration": 21644458, "timestamp": 824433796, "id": 9, "parentId": 3, "tags": {"inputPage": "/login/page"}, "startTime": 1748249274635, "traceId": "2c4657cd13461f7c"}]